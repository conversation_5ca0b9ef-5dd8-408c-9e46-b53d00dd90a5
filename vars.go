package omnichannel_product_list

const (
	DefaultHost = "https://hipos-saas-qa.hexcloud.cn"

	DefaultChannelProductListApi = "/api/omnichannel/channel/product/list/v2"
	DefaultChannelProductInfoApi = "/api/omnichannel/channel/product/productInfo/list"
	DefaultChannelProductAttrApi = "/api/omnichannel/channel/product/attr/list"
	DefaultPriceApi              = "/api/v1/price-center/get-product-price.go-full-info-agent"
	DefaultBaseDataAPi           = "/api/menu-center-pos/menu/queryPosMenuProduct"
)

const (
	DefaultPriceFileName       = "price.json"
	DefaultProductListFileName = "product_list.json"
	DefaultProductInfoFileName = "product_info.json"
	DefaultProductAttrFileName = "product_attr.json"
	DefaultPriceTypeFileName   = "price_type.json"
	DefaultStockListFileName   = "stock_list.json"
)

// 价格过滤相关
const (
	PriceUnavailable = float64(-9999)
	FLEX_SET         = "FLEX_SET"
)
