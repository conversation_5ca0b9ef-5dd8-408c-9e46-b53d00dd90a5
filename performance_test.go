package omnichannel_product_list

import (
	"context"
	"testing"
	"time"
)

// TestFileIOOptimization 测试文件IO优化效果
func TestFileIOOptimization(t *testing.T) {
	// 创建测试数据
	testData := []byte("test data for performance optimization testing")
	
	// 测试带缓冲的写入
	err := bufferedWriteFile("./test_data/test_file.txt", testData)
	if err != nil {
		t.Fatalf("bufferedWriteFile failed: %v", err)
	}
	
	// 测试带缓冲的读取
	data, err := bufferedReadFile("./test_data/test_file.txt")
	if err != nil {
		t.Fatalf("bufferedReadFile failed: %v", err)
	}
	
	if string(data) != string(testData) {
		t.<PERSON>rf("读取的数据不匹配，期望: %s, 实际: %s", string(testData), string(data))
	}
}

// BenchmarkOriginalFileIO 原始文件IO性能基准测试
func BenchmarkOriginalFileIO(b *testing.B) {
	testData := make([]byte, 1024*1024) // 1MB测试数据
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 写入测试
		err := DefaultSaveFunc("./test_data")([]byte("test"), "benchmark_original.txt")
		if err != nil {
			b.Fatalf("DefaultSaveFunc failed: %v", err)
		}
		
		// 读取测试
		_, err = DefaultGetFunc("./test_data")("benchmark_original.txt")
		if err != nil {
			b.Fatalf("DefaultGetFunc failed: %v", err)
		}
	}
}

// BenchmarkOptimizedFileIO 优化后的文件IO性能基准测试
func BenchmarkOptimizedFileIO(b *testing.B) {
	testData := make([]byte, 1024*1024) // 1MB测试数据
	for i := range testData {
		testData[i] = byte(i % 256)
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// 写入测试
		err := DefaultSaveFuncWithBuffer("./test_data")([]byte("test"), "benchmark_optimized.txt")
		if err != nil {
			b.Fatalf("DefaultSaveFuncWithBuffer failed: %v", err)
		}
		
		// 读取测试
		_, err = DefaultGetFuncWithBuffer("./test_data")("benchmark_optimized.txt")
		if err != nil {
			b.Fatalf("DefaultGetFuncWithBuffer failed: %v", err)
		}
	}
}

// BenchmarkLoadLocalMenuData 原始loadLocalMenuData性能基准测试
func BenchmarkLoadLocalMenuData(b *testing.B) {
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./test_data"),
		saveFunc:       DefaultSaveFunc("./test_data"),
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := loadLocalMenuData(ctx, opts)
		if err != nil {
			// 预期会出错，因为测试数据目录不存在
		}
	}
}

// BenchmarkLoadLocalMenuDataOptimized 优化后的loadLocalMenuData性能基准测试
func BenchmarkLoadLocalMenuDataOptimized(b *testing.B) {
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFuncWithBuffer("./test_data"),
		saveFunc:       DefaultSaveFuncWithBuffer("./test_data"),
	}
	
	ctx := context.Background()
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := loadLocalMenuDataOptimized(ctx, opts)
		if err != nil {
			// 预期会出错，因为测试数据目录不存在
		}
	}
}

// TestCacheMechanism 测试缓存机制
func TestCacheMechanism(t *testing.T) {
	// 创建测试数据
	testData := []byte("cache test data")
	
	// 写入测试文件
	err := bufferedWriteFile("./test_data/cache_test.txt", testData)
	if err != nil {
		t.Fatalf("bufferedWriteFile failed: %v", err)
	}
	
	// 第一次读取（缓存未命中）
	start := time.Now()
	data1, err := bufferedReadFile("./test_data/cache_test.txt")
	if err != nil {
		t.Fatalf("第一次读取失败: %v", err)
	}
	firstReadTime := time.Since(start)
	
	// 第二次读取（缓存命中）
	start = time.Now()
	data2, err := bufferedReadFile("./test_data/cache_test.txt")
	if err != nil {
		t.Fatalf("第二次读取失败: %v", err)
	}
	secondReadTime := time.Since(start)
	
	// 验证数据一致性
	if string(data1) != string(data2) {
		t.Errorf("两次读取的数据不一致")
	}
	
	// 验证缓存命中更快（这里只是验证逻辑，实际环境中可能因为各种因素有所不同）
	t.Logf("第一次读取耗时: %v, 第二次读取耗时: %v", firstReadTime, secondReadTime)
	
	// 等待缓存过期
	time.Sleep(cacheTTL + time.Second)
	
	// 第三次读取（缓存已过期）
	start = time.Now()
	data3, err := bufferedReadFile("./test_data/cache_test.txt")
	if err != nil {
		t.Fatalf("第三次读取失败: %v", err)
	}
	thirdReadTime := time.Since(start)
	
	// 验证数据一致性
	if string(data1) != string(data3) {
		t.Errorf("数据不一致")
	}
	
	t.Logf("缓存过期后读取耗时: %v", thirdReadTime)
}