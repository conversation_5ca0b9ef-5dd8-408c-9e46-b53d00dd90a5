package omnichannel_product_list

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"github.com/buger/jsonparser"
	"github.com/goccy/go-json"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
)

func GetPriceInfoFromFile(queryTime int64, getFunc func(string) ([]byte, error), saveFunc func([]byte, string) error) (*model.PriceInfo, error) {
	// 读取文件内容
	start := time.Now()
	bs, err := getFunc(DefaultPriceFileName)
	if err != nil {
		return nil, fmt.Errorf("读取价格信息文件失败: %w", err)
	}

	priceInfo := &model.PriceInfo{}

	if len(bs) == 0 {
		logger.Infof("本地文件price为空,跳过")
		return priceInfo, nil
	}

	if err := json.Unmarshal(bs, priceInfo); err != nil {
		return nil, fmt.Errorf("解析价格信息失败: %w", err)
	}
	logger.Infof("成功读取本地文件: price，大小: %s,耗时：%v", formatDataSize(len(bs)), time.Since(start))

	// 处理当前生效的modify
	priceInfoModel, hasEffective, err := checkEffective(priceInfo, queryTime)
	if err != nil {
		return nil, fmt.Errorf("检查价格生效状态失败: %w", err)
	}
	if queryTime == 0 && hasEffective {
		bs, err = json.Marshal(priceInfoModel)
		if err := saveFunc(bs, DefaultPriceFileName); err != nil {
			return nil, fmt.Errorf("保存价格信息失败: %w", err)
		}
	}
	return priceInfoModel, nil
}

// buildPriceInfoByResponse 根据API响应构建价格信息
// respBody: API响应体
// currentInfo: 当前价格信息（可能为nil）
// queryType: 查询类型
func buildPriceInfoByResponse(respBody []byte, currentInfo *model.PriceInfo, queryType PriceQueryType) (*model.PriceInfo, bool, error) {
	// 检查响应是否为空
	hasUpdated := false
	if len(respBody) == 0 {
		return nil, false, errors.New("响应体为空")
	}

	// 使用jsonparser解析payload部分
	payload, _, _, err := jsonparser.Get(respBody, "payload", "priceCenter")
	if err != nil {
		return nil, false, fmt.Errorf("解析payload字段失败: %w", err)
	}
	if len(payload) == 0 {
		return nil, false, errors.New("响应中缺少payload.priceCenter字段")
	}
	logger.Infof("成功获取云端数据: price，大小: %s", formatDataSize(len(payload)))
	priceResp := &model.PriceResp{}
	err = json.Unmarshal(payload, priceResp)
	if err != nil {
		return nil, false, fmt.Errorf("解析priceCenter失败: %w", err)
	}

	// 获取拉取时间
	if priceResp.PullTime == "" {
		return nil, false, errors.New("响应中缺少pull_time字段")
	}

	// 获取查询类型
	if qt, ok := PriceQueryTypeMap[priceResp.QueryType]; ok {
		queryType = qt
	}

	// 初始化结果
	result := currentInfo

	switch queryType {
	// 全量查询 直接覆盖
	case PriceQueryTypeAll, PriceQueryTypeCurrent:
		if priceResp.Current != nil {
			result.Rows = priceResp.Current.Items
			hasUpdated = true
		}
		// 只查询modify 则合并
	case PriceQueryTypeModify:
		// 查询修改价格，提取 modify 部分
		if priceResp.Modify == nil {
			logger.Infof("price:本地数据和云端数据一致，使用本地数据")
		}
		if priceResp.Modify != nil {
			result, err = mergeModifyIntoFile(result, priceResp.Modify.Items)
			if err != nil {
				return nil, hasUpdated, fmt.Errorf("合并修改信息失败: %w", err)
			}
			// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
			result, _, err = checkEffective(result, 0)
			if err != nil {
				return nil, hasUpdated, fmt.Errorf("检查价格生效状态失败: %w", err)
			}
			hasUpdated = true
		}

	default:
		return nil, hasUpdated, errors.New("不支持的查询类型")
	}

	// 设置批次ID和拉取时间
	result.BatchId = priceResp.BatchId
	result.PullTime = priceResp.PullTime
	return result, hasUpdated, nil
}

// mergeModifyIntoFile 合并当前信息和修改信息
func mergeModifyIntoFile(currentInfo *model.PriceInfo, modifyItems []*model.ModifyProductPriceRow) (*model.PriceInfo, error) {
	// 检查输入
	if len(modifyItems) == 0 {
		return currentInfo, nil
	}

	var err error

	// 合并项目
	currentInfo.Rows, err = mergeItems(currentInfo.Rows, modifyItems)
	if err != nil {
		return nil, fmt.Errorf("合并项目失败: %w", err)
	}
	return currentInfo, err
}

// mergeItems 合并两个项目数组，以item_id+price_type_id为键
// 如果修改项目中有相同的item_id，则替换当前项目中的相应项目
func mergeItems(currentItems []*model.PriceAgent, modifyItems []*model.ModifyProductPriceRow) ([]*model.PriceAgent, error) {

	if len(modifyItems) == 0 {
		return currentItems, nil
	}

	// 将当前项目转换为map，以item_id为键
	currentItemsMap := make(map[string]int)

	for i, item := range currentItems {
		itemID := item.ItemId
		priceTypeID := item.PriceTypeId
		key := fmt.Sprintf("%s-%s", itemID, priceTypeID)
		currentItemsMap[key] = i
	}

	// 处理修改项目，更新或添加到map中

	for _, modifyItem := range modifyItems {
		key := fmt.Sprintf("%s-%s", modifyItem.ItemId, modifyItem.PriceTypeId)

		// 检查当前项目中是否已存在该item_id-price_type_id
		idx, exists := currentItemsMap[key]
		if exists {
			// 如果存在，将修改项目的modify字段合并到当前项目的modify后面
			currentItems[idx].Modify = append(currentItems[idx].Modify, modifyItem.Modify...)
		}
		if !exists {
			// 如果不存在，直接添加修改项目
			currentItems = append(currentItems, &model.PriceAgent{
				ItemId:      modifyItem.ItemId,
				ItemCode:    modifyItem.ItemCode,
				ItemName:    modifyItem.ItemName,
				PriceTypeId: modifyItem.PriceTypeId,
				Modify:      modifyItem.Modify,
			})

		}
	}

	return currentItems, nil
}

func getBatchIdAndQueryType(content *model.PriceInfo, expireInterval time.Duration) (batchId string, queryType PriceQueryType, err error) {

	//默认拉取全量
	batchId = content.BatchId
	queryType = PriceQueryTypeAll
	if content == nil {
		return
	}
	if content.PullTime == "" {
		return
	}
	pullTime, err := time.Parse(time.RFC3339, content.PullTime)
	if err != nil {
		logger.Errorf("解析pull_time失败: %v", err)
		return
	}
	// 如果距离上次拉取时间超过3天，则重新拉取完整价格信息
	if time.Since(pullTime) > expireInterval {
		batchId = "0"
	}

	if batchId != "0" && batchId != "" {
		queryType = PriceQueryTypeModify
	}
	return
}

// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
func checkEffective(priceInfo *model.PriceInfo, queryTime int64) (*model.PriceInfo, bool, error) {
	hasEffective := false
	// 当前时间
	if queryTime == 0 {
		queryTime = time.Now().Unix()
	}

	qt := time.Unix(queryTime, 0)

	// 遍历所有项目，检查modify元素是否已生效
	for _, row := range priceInfo.Rows {
		// 获取modify字段

		// 如果没有modify元素，跳过
		if len(row.Modify) == 0 {
			continue
		}

		// 检查每个modify元素是否已生效
		var effectiveModify *model.PriceModifyInfo
		// 最近生效的时间
		var latestEffectiveTime time.Time
		remainingModifies := make([]*model.PriceModifyInfo, 0, len(row.Modify))

		for _, modifyItem := range row.Modify {

			// 解析effective_time
			effectiveTime, err := time.Parse(time.RFC3339, modifyItem.EffectiveTime)
			if err != nil {
				return nil, false, fmt.Errorf("解析effective_time字段失败: %w", err)
			}

			// 检查是否已生效
			if qt.After(effectiveTime) || qt.Equal(effectiveTime) {
				// 已生效，添加到effectiveModifies
				if effectiveTime.After(latestEffectiveTime) {
					effectiveModify = modifyItem
					latestEffectiveTime = effectiveTime
					hasEffective = true
				}
			} else {
				// 未生效，保留该modify元素
				remainingModifies = append(remainingModifies, modifyItem)
			}
		}

		// 如果有已生效的modify元素，更新当前项目
		if effectiveModify != nil {
			//Price:        effectiveModify.Price,
			//				TaxRate:      effectiveModify.TaxRate,
			//				UseDate:      effectiveModify.UseDate,
			//				Hidden:       effectiveModify.Hidden,
			//				TakeoutPrice: effectiveModify.TakeoutPrice,
			row.Price = effectiveModify.Price
			row.TaxRate = effectiveModify.TaxRate
			row.UseDate = effectiveModify.UseDate
			row.Hidden = effectiveModify.Hidden
			row.TakeoutPrice = effectiveModify.TakeoutPrice
			row.Modify = remainingModifies
			row.ExtendFields = effectiveModify.ExtendedFields
			row.IsValid = effectiveModify.IsValid
		}
	}
	return priceInfo, hasEffective, nil
}

func getFilePath(dir string, fileName string) string {
	return filepath.Join(dir, fileName)
}

func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(priceInfo []byte, fileName string) error {
		savePath := getFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("保存路径不能为空")
		}
		dir := filepath.Dir(savePath)
		if dir != "" && dir != "." { // 只为子目录创建文件夹
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("创建目录失败: %w", err)
			}
		}
		// 总是尝试写入文件
		if err := os.WriteFile(savePath, priceInfo, 0644); err != nil {
			return fmt.Errorf("写入文件失败: %w", err)
		}
		return nil
	}
}

func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := getFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		// get 从文件中读取价格信息
		if savePath == "" {
			return nil, errors.New("保存路径不能为空")
		}
		// 读取文件
		return os.ReadFile(savePath)
	}
}

func getHttpResponse(httpReq *http.Request, client *http.Client) ([]byte, error) {

	// 发送请求
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close() // 确保在函数退出前关闭响应体

	// 检查状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	//判断status_code
	statusCode, err := jsonparser.GetInt(respBody, "status_code")
	if err != nil {
		return nil, fmt.Errorf("读取响应状态码失败: %w", err)

	}
	if statusCode != 200 {
		return nil, fmt.Errorf("API返回错误状态码: %d,resp:%s", statusCode, respBody)

	}
	//请求成功，跳出循环
	return respBody, nil
}
