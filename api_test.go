package omnichannel_product_list

import (
	"context"
	"testing"
)

// TestNewAPIOptions 测试新API的选项配置
func TestNewAPIOptions(t *testing.T) {
	// 测试基础配置选项
	opts := &GetMenuDataOptions{}

	WithHost("https://test.example.com")(opts)
	if opts.Host != "https://test.example.com" {
		t.<PERSON>rrorf("WithHost failed, expected: https://test.example.com, got: %s", opts.Host)
	}

	WithToken("test-token")(opts)
	if opts.Token != "test-token" {
		t.<PERSON><PERSON>rf("WithToken failed, expected: test-token, got: %s", opts.Token)
	}

	WithStoreID("12345")(opts)
	if opts.StoreID != "12345" {
		t.<PERSON>rf("WithStoreID failed, expected: 12345, got: %s", opts.StoreID)
	}

	WithChannelCode("POS")(opts)
	if opts.ChannelCode != "POS" {
		t.<PERSON>("WithChannelCode failed, expected: POS, got: %s", opts.ChannelCode)
	}

	WithLang("zh-CN")(opts)
	if opts.Lang != "zh-CN" {
		t.<PERSON><PERSON><PERSON>("With<PERSON><PERSON> failed, expected: zh-CN, got: %s", opts.Lang)
	}

	// 测试目录配置选项
	WithProdDir("/data/prod")(opts)
	if opts.ProdDir != "/data/prod" {
		t.Errorf("WithProdDir failed, expected: /data/prod, got: %s", opts.ProdDir)
	}

	WithTempDir("/data/temp")(opts)
	if opts.TempDir != "/data/temp" {
		t.Errorf("WithTempDir failed, expected: /data/temp, got: %s", opts.TempDir)
	}

	// 测试操作模式选项
	WithMode(ModeFetchOnly)(opts)
	if opts.Mode != ModeFetchOnly {
		t.Errorf("WithMode failed, expected: ModeFetchOnly, got: %d", opts.Mode)
	}

	WithAutoMerge(true)(opts)
	if !opts.AutoMerge {
		t.Error("WithAutoMerge failed, expected: true")
	}
}

// TestGetMenuDataFunction 测试GetMenuData函数的基本功能
func TestGetMenuDataFunction(t *testing.T) {
	// 测试默认配置
	_ = context.Background()

	// 由于实际实现尚未完成，这里只测试函数是否能被调用
	// 在实际使用中，这里会进行完整的功能测试

	// 测试不同模式
	testCases := []struct {
		mode GetDataMode
		name string
	}{
		{ModeAutoMerge, "ModeAutoMerge"},
		{ModeFetchOnly, "ModeFetchOnly"},
		{ModeMergeTemp, "ModeMergeTemp"},
		{ModeProdOnly, "ModeProdOnly"},
	}
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 测试模式设置
			opts := &GetMenuDataOptions{Mode: tc.mode}
			if opts.Mode != tc.mode {
				t.Errorf("Mode setting failed, expected: %d, got: %d", tc.mode, opts.Mode)
			}
		})
	}
}

// TestFetchToTempFunction 测试FetchToTemp函数
func TestFetchToTempFunction(t *testing.T) {
	//
	//storeId := uint64(4908316407994810368)
	//storeId := uint64(4877537183948374016)
	//host := "https://hipos-saas-qa.hexcloud.cn"
	opts := &GetMenuDataOptions{
		Host:        "https://hipos-saas-qa.hexcloud.cn",
		Token:       "lwOiN2Y0OgXUgq0A2fJW_9aeEiN0Gfht7YfZm6w7vAU.gYFYM9UzvPgz1UnOf_dh1ALG5-OgFrK5wopaJJltUsI",
		StoreID:     "4877537183948374016",
		ChannelCode: "POS",
		ProdDir:     "./json/prod",
		TempDir:     "./json/dev",
		Mode:        ModeFetchOnly,
	}

	err := FetchToTempV2(context.Background(), WithTempDir(opts.TempDir), WithStoreID(opts.StoreID), WithProdDir(opts.ProdDir), WithToken(opts.Token))
	if err != nil {
		t.Errorf("FetchToTemp failed: %v", err)
	}
}

// TestMergeTempToProdFunction 测试MergeTempToProd函数
func TestMergeTempToProdFunction(t *testing.T) {
	opts := &GetMenuDataOptions{
		Host:        "https://hipos-saas-qa.hexcloud.cn",
		Token:       "lwOiN2Y0OgXUgq0A2fJW_9aeEiN0Gfht7YfZm6w7vAU.gYFYM9UzvPgz1UnOf_dh1ALG5-OgFrK5wopaJJltUsI",
		StoreID:     "4877537183948374016",
		ChannelCode: "POS",
		ProdDir:     "./json/prod",
		TempDir:     "./json/dev",
		Mode:        ModeFetchOnly,
	}

	err := MergeTempToProdV2(context.Background(), WithProdDir(opts.ProdDir), WithTempDir(opts.TempDir))
	if err != nil {
		t.Errorf("MergeTempToProd failed: %v", err)
	}
}

// TestHasTempUpdateFunction 测试HasTempUpdate函数
func TestHasTempUpdateFunction(t *testing.T) {
	opts := &GetMenuDataOptions{
		Host:        "https://hipos-saas-qa.hexcloud.cn",
		Token:       "lwOiN2Y0OgXUgq0A2fJW_9aeEiN0Gfht7YfZm6w7vAU.gYFYM9UzvPgz1UnOf_dh1ALG5-OgFrK5wopaJJltUsI",
		StoreID:     "4877537183948374016",
		ChannelCode: "POS",
		ProdDir:     "./json/prod",
		TempDir:     "./json/dev",
		Mode:        ModeFetchOnly,
	}
	_ = FetchToTempV2(context.Background(), WithTempDir(opts.TempDir), WithStoreID(opts.StoreID), WithProdDir(opts.ProdDir), WithToken(opts.Token))
	hasUpdate, err := HasTempUpdateV2(WithProdDir(opts.ProdDir), WithTempDir(opts.TempDir))
	if err != nil || !hasUpdate {
		t.Errorf("HasTempUpdate failed: %v", err)
	}
}
