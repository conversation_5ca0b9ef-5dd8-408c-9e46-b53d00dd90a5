package omnichannel_product_list

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

// TestGlobalHTTPClientReuse 测试全局HTTP客户端是否被正确复用
func TestGlobalHTTPClientReuse(t *testing.T) {
	// 创建一个测试服务器
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status_code": 200, "message": "success"}`))
	}))
	defer server.Close()

	// 创建测试参数
	params := &GetMenuParams{
		StoreID:     "12345",
		Lang:        "zh-CN",
		ChannelCode: "POS",
		localData: &productListOpt{
			OriginProductListMd5:    new(string),
			OriginProductSpuInfoMd5: new(string),
			OriginAttrListMd5:       new(string),
			OriginPriceTypeMd5:      new(string),
		},
	}

	// 创建选项配置
	opts := &pullDataOption{
		maxRetries: 1,
		retryDelay: 100 * time.Millisecond,
		getFunc:    DefaultGetFunc("./"),
		saveFunc:   DefaultSaveFunc("./"),
	}

	// 发送多个请求测试HTTP客户端复用
	for i := 0; i < 3; i++ {
		_, err := sendBaseDataRequest(context.Background(), server.URL, "test-token", params, opts)
		if err != nil {
			t.Errorf("sendBaseDataRequest failed: %v", err)
		}
	}

	// 验证使用的是全局HTTP客户端
	if defaultHTTPClient == nil {
		t.Error("defaultHTTPClient is nil")
	}
}
