package menukit

import (
	"context"
	"os"
	"testing"
	"time"
)

// BenchmarkGetMenuData benchmarks the GetMenuData function
func BenchmarkGetMenuData(b *testing.B) {
	// Create test directories
	testDir := "./bench_test_data"
	defer os.RemoveAll(testDir)

	// Create test data files
	setupBenchmarkData(b, testDir)

	// Reset timer
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		_, err := GetMenuDataFromProd(
			context.Background(),
			WithProdDir(testDir),
			WithStoreID(12345),
			WithChannelCode("POS"),
		)
		if err != nil {
			// Expected to fail due to missing data, but we're testing performance
			continue
		}
	}
}

// BenchmarkFileOperations benchmarks file read/write operations
func BenchmarkFileOperations(b *testing.B) {
	testDir := "./bench_file_ops"
	defer os.RemoveAll(testDir)

	testData := make([]byte, 1024*1024) // 1MB test data
	for i := range testData {
		testData[i] = byte(i % 256)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Write test
		saveFunc := DefaultSaveFunc(testDir)
		err := saveFunc(testData, "benchmark.json")
		if err != nil {
			b.Fatalf("DefaultSaveFunc failed: %v", err)
		}

		// Read test
		getFunc := DefaultGetFunc(testDir)
		_, err = getFunc("benchmark.json")
		if err != nil {
			b.Fatalf("DefaultGetFunc failed: %v", err)
		}
	}
}

// BenchmarkDirectoryOperations benchmarks directory management operations
func BenchmarkDirectoryOperations(b *testing.B) {
	tempDir := "./bench_temp"
	prodDir := "./bench_prod"
	defer func() {
		os.RemoveAll(tempDir)
		os.RemoveAll(prodDir)
	}()

	// Setup test data
	setupBenchmarkData(b, tempDir)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Benchmark MergeTempToProd
		err := MergeTempToProd(
			context.Background(),
			WithTempDir(tempDir),
			WithProdDir(prodDir),
		)
		if err != nil {
			b.Fatalf("MergeTempToProd failed: %v", err)
		}

		// Benchmark HasTempUpdate
		_, err = HasTempUpdate(
			WithTempDir(tempDir),
			WithProdDir(prodDir),
		)
		if err != nil {
			b.Fatalf("HasTempUpdate failed: %v", err)
		}
	}
}

// BenchmarkOptionApplication benchmarks option application
func BenchmarkOptionApplication(b *testing.B) {
	options := []Option{
		WithHost("https://test.example.com"),
		WithToken("test-token"),
		WithStoreID(12345),
		WithChannelCode("POS"),
		WithLang("zh-CN"),
		WithProdDir("./prod"),
		WithTempDir("./temp"),
		WithMode(ModeAutoMerge),
		WithMaxRetries(3),
		WithRetryDelay(500 * time.Millisecond),
		WithExpireInterval(24 * time.Hour),
		WithNotFilterByPrice(false),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		opts := &Options{}
		for _, option := range options {
			option(opts)
		}
	}
}

// BenchmarkPullDataOptionApplication benchmarks PullDataOption application
func BenchmarkPullDataOptionApplication(b *testing.B) {
	options := []PullDataOption{
		WithGetFunc(DefaultGetFunc("./")),
		WithSaveFunc(DefaultSaveFunc("./")),
		WithHTTPClient(GlobalHTTPClient),
		WithReturnBaseData(true),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		opts := applyPullPriceOptions(options...)
		_ = opts // Use the result to prevent optimization
	}
}

// setupBenchmarkData creates test data files for benchmarking
func setupBenchmarkData(b *testing.B, dir string) {
	err := os.MkdirAll(dir, 0755)
	if err != nil {
		b.Fatalf("Failed to create test directory: %v", err)
	}

	testFiles := map[string]string{
		DefaultPriceFileName: `{
			"batchId": "123456",
			"pullTime": "2023-01-01T00:00:00Z",
			"rows": []
		}`,
		DefaultProductListFileName: `{
			"md5": "test-md5",
			"rows": []
		}`,
		DefaultProductInfoFileName: `{
			"md5": "test-md5",
			"rows": []
		}`,
		DefaultProductAttrFileName: `{
			"md5": "test-md5",
			"rows": []
		}`,
		DefaultPriceTypeFileName: `{
			"md5": "test-md5",
			"rows": []
		}`,
	}

	for fileName, content := range testFiles {
		err := os.WriteFile(dir+"/"+fileName, []byte(content), 0644)
		if err != nil {
			b.Fatalf("Failed to write test file %s: %v", fileName, err)
		}
	}
}

// TestPerformanceOptimization tests performance optimization features
func TestPerformanceOptimization(t *testing.T) {
	// Test HTTP client reuse
	if GlobalHTTPClient == nil {
		t.Error("GlobalHTTPClient should not be nil")
	}

	// Test that the same client instance is used
	client1 := GlobalHTTPClient
	client2 := GlobalHTTPClient
	if client1 != client2 {
		t.Error("GlobalHTTPClient should be the same instance")
	}

	// Test timeout configuration
	if GlobalHTTPClient.Timeout != 30*time.Second {
		t.Errorf("Expected timeout 30s, got %v", GlobalHTTPClient.Timeout)
	}
}

// TestConcurrentOperations tests concurrent safety
func TestConcurrentOperations(t *testing.T) {
	testDir := "./test_concurrent"
	defer os.RemoveAll(testDir)

	// Setup test data
	err := os.MkdirAll(testDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	testData := []byte(`{"test": "concurrent"}`)
	err = os.WriteFile(testDir+"/"+DefaultPriceFileName, testData, 0644)
	if err != nil {
		t.Fatalf("Failed to write test file: %v", err)
	}

	// Test concurrent file operations
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func(id int) {
			defer func() { done <- true }()
			
			getFunc := DefaultGetFunc(testDir)
			data, err := getFunc(DefaultPriceFileName)
			if err != nil {
				t.Errorf("Concurrent read failed: %v", err)
				return
			}
			if len(data) == 0 {
				t.Error("Concurrent read returned empty data")
			}
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}
}
