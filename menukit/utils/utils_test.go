package utils

import (
	"testing"
)

func TestUtilsFunctions(t *testing.T) {
	// Test FormatDataSize function
	tests := []struct {
		bytes    int
		expected string
	}{
		{0, "0 B"},
		{1023, "1023 B"},
		{1024, "1.00 KB"},
		{1024*1024 - 1, "1024.00 KB"},
		{1024 * 1024, "1.00 MB"},
		{1024*1024*1024 - 1, "1024.00 MB"},
		{1024 * 1024 * 1024, "1.00 GB"},
	}

	for _, test := range tests {
		result := FormatDataSize(test.bytes)
		if result != test.expected {
			t.Errorf("FormatDataSize(%d) = %s; expected %s", test.bytes, result, test.expected)
		}
	}

	// Test GetFilePath function
	path := GetFilePath("/test", "file.txt")
	expected := "/test/file.txt"
	if path != expected {
		t.Errorf("GetFilePath(/test, file.txt) = %s; expected %s", path, expected)
	}

	// Test EnsureDir function with a temporary directory
	// This would require more complex setup and is omitted for brevity
}