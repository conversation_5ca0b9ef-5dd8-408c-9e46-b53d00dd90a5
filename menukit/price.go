package menukit

import (
	"errors"
	"fmt"
	"github.com/buger/jsonparser"
	"github.com/goccy/go-json"
	"github.com/spf13/cast"
	"hexcloud.cn/hicloud/menukit/models"
	"hexcloud.cn/hicloud/menukit/utils"
	"os"
	"path/filepath"
	"time"
)

func GetPriceInfoFromFile(queryTime int64, getFunc func(string) ([]byte, error), saveFunc func([]byte, string) error) (*models.PriceInfo, error) {
	// Read file content
	start := time.Now()
	bs, err := getFunc(DefaultPriceFileName)
	if err != nil {
		return nil, fmt.Errorf("failed to read price information file: %w", err)
	}

	priceInfo := &models.PriceInfo{}

	if len(bs) == 0 {
		logger.Infof("Local file price is empty, skipping")
		return priceInfo, nil
	}
	if err := json.Unmarshal(bs, priceInfo); err != nil {
		return nil, fmt.Errorf("failed to parse price information: %w", err)
	}
	logger.Infof("Successfully read local file: price, size: %s, time: %v", formatDataSize(len(bs)), time.Since(start))

	// Process currently effective modify
	priceInfoModel, hasEffective, err := checkEffective(priceInfo, queryTime)
	if err != nil {
		return nil, fmt.Errorf("failed to check price effectiveness status: %w", err)
	}
	if queryTime == 0 && hasEffective {
		bs, err = json.Marshal(priceInfoModel)
		if err := saveFunc(bs, DefaultPriceFileName); err != nil {
			return nil, fmt.Errorf("failed to save price information: %w", err)
		}
	}
	return priceInfoModel, nil
}

// buildPriceInfoByResponse builds price information based on API response
// respBody: API response body
// currentInfo: current price information (may be nil)
// queryType: query type
func buildPriceInfoByResponse(respBody []byte, currentInfo *models.PriceInfo, queryType PriceQueryType) (*models.PriceInfo, error) {
	// Check if response is empty
	if len(respBody) == 0 {
		return nil, errors.New("response body is empty")
	}

	// Use jsonparser to parse payload part
	payload, _, _, err := jsonparser.Get(respBody, "payload", "priceCenter")
	if err != nil {
		return nil, fmt.Errorf("failed to parse payload field: %w", err)
	}
	if len(payload) == 0 {
		return nil, errors.New("missing payload.priceCenter field in response")
	}
	logger.Infof("Successfully obtained cloud data: price, size: %s", formatDataSize(len(payload)))
	priceResp := &models.PriceResp{}
	err = json.Unmarshal(payload, priceResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse priceCenter: %w", err)
	}

	// Get pull time
	if priceResp.PullTime == "" {
		return nil, errors.New("missing pull_time field in response")
	}

	// Get query type
	if qt, ok := PriceQueryTypeMap[priceResp.QueryType]; ok {
		queryType = qt
	}

	// Initialize result
	result := currentInfo

	switch queryType {
	// Full query, directly overwrite
	case PriceQueryTypeAll, PriceQueryTypeCurrent:
		result.Rows = priceResp.Current.Items

		// Only query modify then merge
	case PriceQueryTypeModify:
		// Query modified price, extract modify part
		if priceResp.Modify == nil {
			logger.Infof("price: local data and cloud data are consistent, using local data")
		}
		if priceResp.Modify != nil {
			result, err = mergeModifyIntoFile(result, priceResp.Modify.Items)
			if err != nil {
				return nil, fmt.Errorf("failed to merge modification information: %w", err)
			}
			// When querying incrementally, need to check if modify elements are effective,
			// if effective, update rows and delete elements from modify
			result, _, err = checkEffective(result, 0)
			if err != nil {
				return nil, fmt.Errorf("failed to check price effectiveness status: %w", err)
			}
		}

	default:
		return nil, errors.New("unsupported query type")
	}

	// Set batch ID and pull time
	result.BatchId = priceResp.BatchId
	result.PullTime = priceResp.PullTime
	return result, nil
}

// mergeModifyIntoFile merges current information and modification information
func mergeModifyIntoFile(currentInfo *models.PriceInfo, modifyItems []*models.ModifyProductPriceRow) (*models.PriceInfo, error) {
	// Check input
	if len(modifyItems) == 0 {
		return currentInfo, nil
	}

	var err error

	// Merge items
	currentInfo.Rows, err = mergeItems(currentInfo.Rows, modifyItems)
	if err != nil {
		return nil, fmt.Errorf("failed to merge items: %w", err)
	}
	return currentInfo, err
}

// mergeItems merges two item arrays, using item_id+price_type_id as key
// If modified items have the same item_id, replace the corresponding item in current items
func mergeItems(currentItems []*models.PriceAgent, modifyItems []*models.ModifyProductPriceRow) ([]*models.PriceAgent, error) {
	if len(modifyItems) == 0 {
		return currentItems, nil
	}

	// Convert current items to map, using item_id as key
	currentItemsMap := make(map[string]int)

	for i, item := range currentItems {
		itemID := item.ItemId
		priceTypeID := item.PriceTypeId
		key := fmt.Sprintf("%s-%s", itemID, priceTypeID)
		currentItemsMap[key] = i
	}

	// Process modification items, update or add to map
	for _, modifyItem := range modifyItems {
		key := fmt.Sprintf("%s-%s", modifyItem.ItemId, modifyItem.PriceTypeId)

		// Check if the item_id-price_type_id already exists in current items
		idx, exists := currentItemsMap[key]
		if exists {
			// If exists, merge the modify field of modification items to the modify field of current items
			currentItems[idx].Modify = append(currentItems[idx].Modify, modifyItem.Modify...)
		}
		if !exists {
			// If not exists, directly add modification items
			currentItems = append(currentItems, &models.PriceAgent{
				ItemId:      modifyItem.ItemId,
				ItemCode:    modifyItem.ItemCode,
				ItemName:    modifyItem.ItemName,
				PriceTypeId: modifyItem.PriceTypeId,
				Modify:      modifyItem.Modify,
			})
		}
	}

	return currentItems, nil
}

func getBatchIdAndQueryType(content *models.PriceInfo, expireInterval time.Duration) (batchId uint64, queryType PriceQueryType, err error) {
	// Default full fetch
	batchId = cast.ToUint64(content.BatchId)
	queryType = PriceQueryTypeAll
	if content == nil {
		return
	}
	if content.PullTime == "" {
		return
	}
	pullTime, err := time.Parse(time.RFC3339, content.PullTime)
	if err != nil {
		logger.Errorf("Failed to parse pull_time: %v", err)
		return
	}
	// If more than 3 days have passed since last fetch, re-fetch complete price information
	if time.Since(pullTime) > expireInterval {
		batchId = 0
	}

	if batchId != 0 {
		queryType = PriceQueryTypeModify
	}
	return
}

// When querying incrementally, need to check if modify elements are effective,
// if effective, update rows and delete elements from modify
func checkEffective(priceInfo *models.PriceInfo, queryTime int64) (*models.PriceInfo, bool, error) {
	hasEffective := false
	// Current time
	if queryTime == 0 {
		queryTime = time.Now().Unix()
	}

	qt := time.Unix(queryTime, 0)

	// Traverse all items, check if modify elements are effective
	for _, row := range priceInfo.Rows {
		// Get modify field

		// If there are no modify elements, skip
		if len(row.Modify) == 0 {
			continue
		}

		// Check if each modify element is effective
		var effectiveModify *models.PriceModifyInfo
		// Most recent effective time
		var latestEffectiveTime time.Time
		remainingModifies := make([]*models.PriceModifyInfo, 0, len(row.Modify))

		for _, modifyItem := range row.Modify {
			// Parse effective_time
			effectiveTime, err := time.Parse(time.RFC3339, modifyItem.EffectiveTime)
			if err != nil {
				return nil, false, fmt.Errorf("failed to parse effective_time field: %w", err)
			}

			// Check if it's effective
			if qt.After(effectiveTime) || qt.Equal(effectiveTime) {
				// Effective, add to effectiveModifies
				if effectiveTime.After(latestEffectiveTime) {
					effectiveModify = modifyItem
					latestEffectiveTime = effectiveTime
					hasEffective = true
				}
			} else {
				// Not effective, keep the modify element
				remainingModifies = append(remainingModifies, modifyItem)
			}
		}

		// If there are effective modify elements, update current item
		if effectiveModify != nil {
			//Price:        effectiveModify.Price,
			//TaxRate:      effectiveModify.TaxRate,
			//UseDate:      effectiveModify.UseDate,
			//Hidden:       effectiveModify.Hidden,
			//TakeoutPrice: effectiveModify.TakeoutPrice,
			row.Price = effectiveModify.Price
			row.TaxRate = effectiveModify.TaxRate
			row.UseDate = effectiveModify.UseDate
			row.Hidden = effectiveModify.Hidden
			row.TakeoutPrice = effectiveModify.TakeoutPrice
			row.Modify = remainingModifies
			row.ExtendFields = effectiveModify.ExtendedFields
			row.IsValid = true
		}
	}
	return priceInfo, hasEffective, nil
}

func getFilePath(dir string, fileName string) string {
	return utils.GetFilePath(dir, fileName)
}

func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(priceInfo []byte, fileName string) error {
		savePath := utils.GetFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("save path cannot be empty")
		}
		if err := utils.EnsureDir(filepath.Dir(savePath)); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}
		// Always try to write file
		if err := os.WriteFile(savePath, priceInfo, 0644); err != nil {
			return fmt.Errorf("failed to write file: %w", err)
		}
		return nil
	}
}

func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := utils.GetFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		// get price information from file
		if savePath == "" {
			return nil, errors.New("save path cannot be empty")
		}
		// Read file
		return os.ReadFile(savePath)
	}
}
