package menukit

import (
	"context"
	"os"
	"path/filepath"
	"testing"
	"time"
)

// TestNewAPIOptions tests the new API's option configuration
func TestNewAPIOptions(t *testing.T) {
	// Test basic configuration options
	opts := &Options{}

	WithHost("https://test.example.com")(opts)
	if opts.Host != "https://test.example.com" {
		t.Errorf("WithHost failed, expected: https://test.example.com, got: %s", opts.Host)
	}

	WithToken("test-token")(opts)
	if opts.Token != "test-token" {
		t.<PERSON><PERSON>("WithToken failed, expected: test-token, got: %s", opts.Token)
	}

	WithStoreID(12345)(opts)
	if opts.StoreID != 12345 {
		t.<PERSON>rf("WithStoreID failed, expected: 12345, got: %d", opts.StoreID)
	}

	WithChannelCode("POS")(opts)
	if opts.ChannelCode != "POS" {
		t.<PERSON>rrorf("WithChannelCode failed, expected: POS, got: %s", opts.ChannelCode)
	}

	WithLang("zh-CN")(opts)
	if opts.Lang != "zh-CN" {
		t.Errorf("With<PERSON><PERSON> failed, expected: zh-CN, got: %s", opts.Lang)
	}

	// Test directory configuration options
	WithProdDir("/data/prod")(opts)
	if opts.ProdDir != "/data/prod" {
		t.Errorf("WithProdDir failed, expected: /data/prod, got: %s", opts.ProdDir)
	}

	WithTempDir("/data/temp")(opts)
	if opts.TempDir != "/data/temp" {
		t.Errorf("WithTempDir failed, expected: /data/temp, got: %s", opts.TempDir)
	}

	// Test operation mode options
	WithMode(ModeFetchOnly)(opts)
	if opts.Mode != ModeFetchOnly {
		t.Errorf("WithMode failed, expected: ModeFetchOnly, got: %d", opts.Mode)
	}

	WithAutoMerge(true)(opts)
	if !opts.AutoMerge {
		t.Error("WithAutoMerge failed, expected: true")
	}
}

// TestGetMenuDataFunction tests the GetMenuData function's basic functionality
func TestGetMenuDataFunction(t *testing.T) {
	// Test default configuration
	_ = context.Background()

	// Since the actual implementation is not yet complete, this only tests if the function can be called
	// In actual use, this would perform complete functional testing

	// Test different modes
	testCases := []struct {
		mode GetDataMode
		name string
	}{
		{ModeAutoMerge, "ModeAutoMerge"},
		{ModeFetchOnly, "ModeFetchOnly"},
		{ModeMergeTemp, "ModeMergeTemp"},
		{ModeProdOnly, "ModeProdOnly"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test mode setting
			opts := &Options{Mode: tc.mode}
			if opts.Mode != tc.mode {
				t.Errorf("Mode setting failed, expected: %d, got: %d", tc.mode, opts.Mode)
			}
		})
	}
}

// TestFetchToTempFunction tests the FetchToTemp function
func TestFetchToTempFunction(t *testing.T) {
	// Since the actual implementation is not yet complete, this only tests if the function can be compiled
	// In actual use, this would perform complete functional testing

	_ = FetchToTemp(context.Background(), WithTempDir("/tmp"))
}

// TestMergeTempToProdFunction tests the MergeTempToProd function
func TestMergeTempToProdFunction(t *testing.T) {
	// Since the actual implementation is not yet complete, this only tests if the function can be compiled
	// In actual use, this would perform complete functional testing

	_ = MergeTempToProd(context.Background(), WithProdDir("/prod"), WithTempDir("/temp"))
}

// TestHasTempUpdateFunction tests the HasTempUpdate function
func TestHasTempUpdateFunction(t *testing.T) {
	// Create test directories
	tempDir := "./test_temp"
	prodDir := "./test_prod"

	// Clean up test directories
	defer func() {
		os.RemoveAll(tempDir)
		os.RemoveAll(prodDir)
	}()

	// Test when no files exist
	hasUpdate, err := HasTempUpdate(WithTempDir(tempDir), WithProdDir(prodDir))
	if err != nil {
		t.Errorf("HasTempUpdate failed: %v", err)
	}
	if hasUpdate {
		t.Error("HasTempUpdate should return false when no files exist")
	}

	// Create test file in temp directory
	testData := []byte(`{"test": "data"}`)
	err = os.MkdirAll(tempDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	err = os.WriteFile(filepath.Join(tempDir, DefaultPriceFileName), testData, 0644)
	if err != nil {
		t.Fatalf("Failed to write test file: %v", err)
	}

	// Test when temp file exists but prod file doesn't
	hasUpdate, err = HasTempUpdate(WithTempDir(tempDir), WithProdDir(prodDir))
	if err != nil {
		t.Errorf("HasTempUpdate failed: %v", err)
	}
	if !hasUpdate {
		t.Error("HasTempUpdate should return true when temp file exists but prod file doesn't")
	}
}

// TestDirectoryManagement tests directory management functions
func TestDirectoryManagement(t *testing.T) {
	// Create test directories
	tempDir := "./test_temp_mgmt"
	prodDir := "./test_prod_mgmt"

	// Clean up test directories
	defer func() {
		os.RemoveAll(tempDir)
		os.RemoveAll(prodDir)
	}()

	// Create test files in temp directory
	testData := []byte(`{"test": "data", "timestamp": "2023-01-01"}`)
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}

	testFiles := []string{
		DefaultPriceFileName,
		DefaultProductListFileName,
		DefaultProductInfoFileName,
	}

	for _, fileName := range testFiles {
		err = os.WriteFile(filepath.Join(tempDir, fileName), testData, 0644)
		if err != nil {
			t.Fatalf("Failed to write test file %s: %v", fileName, err)
		}
	}

	// Test MergeTempToProd
	err = MergeTempToProd(context.Background(), WithTempDir(tempDir), WithProdDir(prodDir))
	if err != nil {
		t.Errorf("MergeTempToProd failed: %v", err)
	}

	// Verify files were copied
	for _, fileName := range testFiles {
		prodFile := filepath.Join(prodDir, fileName)
		if _, err := os.Stat(prodFile); os.IsNotExist(err) {
			t.Errorf("File %s should have been copied to prod directory", fileName)
		}
	}
}

// TestPullDataOptions tests PullDataOption functions
func TestPullDataOptions(t *testing.T) {
	opts := &pullDataOption{}

	// Test file operation function options
	getFunc := func(fileName string) ([]byte, error) {
		return []byte("test"), nil
	}
	WithGetFunc(getFunc)(opts)
	if opts.getFunc == nil {
		t.Error("WithGetFunc failed, getFunc should not be nil")
	}

	saveFunc := func(data []byte, fileName string) error {
		return nil
	}
	WithSaveFunc(saveFunc)(opts)
	if opts.saveFunc == nil {
		t.Error("WithSaveFunc failed, saveFunc should not be nil")
	}

	// Test HTTP client option
	WithHTTPClient(GlobalHTTPClient)(opts)
	if opts.httpClient != GlobalHTTPClient {
		t.Error("WithHTTPClient failed, httpClient should be set")
	}

	// Test return base data option
	WithReturnBaseData(true)(opts)
	if !opts.returnBaseData {
		t.Error("WithReturnBaseData failed, returnBaseData should be true")
	}
}

// TestDefaultFunctions tests default file operation functions
func TestDefaultFunctions(t *testing.T) {
	testDir := "./test_default_funcs"
	defer os.RemoveAll(testDir)

	// Test DefaultSaveFunc
	saveFunc := DefaultSaveFunc(testDir)
	testData := []byte(`{"test": "data"}`)
	err := saveFunc(testData, "test.json")
	if err != nil {
		t.Errorf("DefaultSaveFunc failed: %v", err)
	}

	// Test DefaultGetFunc
	getFunc := DefaultGetFunc(testDir)
	data, err := getFunc("test.json")
	if err != nil {
		t.Errorf("DefaultGetFunc failed: %v", err)
	}

	if string(data) != string(testData) {
		t.Errorf("Data mismatch, expected: %s, got: %s", string(testData), string(data))
	}

	// Test getting non-existent file
	data, err = getFunc("nonexistent.json")
	if err != nil {
		t.Errorf("DefaultGetFunc should not error for non-existent file: %v", err)
	}
	if data != nil {
		t.Error("DefaultGetFunc should return nil for non-existent file")
	}
}