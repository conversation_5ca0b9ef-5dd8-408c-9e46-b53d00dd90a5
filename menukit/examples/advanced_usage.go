package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"
	"hexcloud.cn/hicloud/menukit"
)

func main() {
	fmt.Println("=== MenuKit Advanced Usage Examples ===")

	// Custom HTTP client with specific timeout and transport settings
	customHTTPClient := &http.Client{
		Timeout: 60 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// Example 1: Using custom file operations
	fmt.Println("\n1. Custom file operations example:")
	customFileExample(customHTTPClient)

	// Example 2: Error handling and retry strategies
	fmt.Println("\n2. Error handling and retry strategies:")
	errorHandlingExample()

	// Example 3: Performance monitoring
	fmt.Println("\n3. Performance monitoring:")
	performanceMonitoringExample()

	// Example 4: Multi-store operations
	fmt.Println("\n4. Multi-store operations:")
	multiStoreExample()

	// Example 5: Data validation and filtering
	fmt.Println("\n5. Data validation and filtering:")
	dataValidationExample()

	fmt.Println("\n=== Advanced Examples Completed ===")
}

// customFileExample demonstrates custom file operations
func customFileExample(httpClient *http.Client) {
	ctx := context.Background()

	// Custom get function with logging
	customGetFunc := func(fileName string) ([]byte, error) {
		fmt.Printf("  Reading file: %s\n", fileName)
		data, err := os.ReadFile("./custom_data/" + fileName)
		if err != nil {
			fmt.Printf("  Failed to read %s: %v\n", fileName, err)
			return nil, nil // Return nil for missing files
		}
		fmt.Printf("  Successfully read %s (%d bytes)\n", fileName, len(data))
		return data, nil
	}

	// Custom save function with backup
	customSaveFunc := func(data []byte, fileName string) error {
		fmt.Printf("  Saving file: %s (%d bytes)\n", fileName, len(data))
		
		// Create backup if file exists
		filePath := "./custom_data/" + fileName
		if _, err := os.Stat(filePath); err == nil {
			backupPath := filePath + ".backup"
			if err := os.Rename(filePath, backupPath); err != nil {
				fmt.Printf("  Warning: Failed to create backup: %v\n", err)
			} else {
				fmt.Printf("  Created backup: %s\n", backupPath)
			}
		}

		// Ensure directory exists
		if err := os.MkdirAll("./custom_data", 0755); err != nil {
			return fmt.Errorf("failed to create directory: %w", err)
		}

		// Write new file
		if err := os.WriteFile(filePath, data, 0644); err != nil {
			return fmt.Errorf("failed to write file: %w", err)
		}

		fmt.Printf("  Successfully saved %s\n", fileName)
		return nil
	}

	// Use custom functions with menukit
	_, err := menukit.GetMenuDataLegacy(
		ctx,
		"https://hipos-saas-qa.hexcloud.cn",
		"your-auth-token",
		&menukit.GetMenuParams{
			StoreID:     12345,
			ChannelCode: "POS",
			Lang:        "zh-CN",
			PartnerID:   "partner123",
			LocalData:   &menukit.ProductListOpt{},
		},
		menukit.WithGetFunc(customGetFunc),
		menukit.WithSaveFunc(customSaveFunc),
		menukit.WithHTTPClient(httpClient),
		menukit.WithMaxRetries(3),
		menukit.WithRetryDelay(2*time.Second),
	)

	if err != nil {
		fmt.Printf("  Custom file operations failed: %v\n", err)
	} else {
		fmt.Println("  Custom file operations completed successfully")
	}
}

// errorHandlingExample demonstrates error handling strategies
func errorHandlingExample() {
	ctx := context.Background()

	// Configuration with aggressive retry settings
	retryConfig := []menukit.Option{
		menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
		menukit.WithToken("your-auth-token"),
		menukit.WithStoreID(12345),
		menukit.WithChannelCode("POS"),
		menukit.WithMaxRetries(5),
		menukit.WithRetryDelay(3 * time.Second),
		menukit.WithProdDir("./error_handling/prod"),
		menukit.WithTempDir("./error_handling/temp"),
	}

	// Try to get menu data with error handling
	menuData, err := menukit.GetMenuData(ctx, retryConfig...)
	if err != nil {
		fmt.Printf("  Primary fetch failed: %v\n", err)
		
		// Fallback to production data only
		fmt.Println("  Attempting fallback to production data...")
		fallbackData, fallbackErr := menukit.GetMenuDataFromProd(
			ctx,
			menukit.WithProdDir("./error_handling/prod"),
			menukit.WithStoreID(12345),
			menukit.WithChannelCode("POS"),
		)
		
		if fallbackErr != nil {
			fmt.Printf("  Fallback also failed: %v\n", fallbackErr)
		} else {
			fmt.Printf("  Fallback successful with %d categories\n", len(fallbackData.Payload.Category))
		}
	} else {
		fmt.Printf("  Primary fetch successful with %d categories\n", len(menuData.Payload.Category))
	}
}

// performanceMonitoringExample demonstrates performance monitoring
func performanceMonitoringExample() {
	ctx := context.Background()

	// Measure different operations
	operations := []struct {
		name string
		fn   func() error
	}{
		{
			name: "Auto merge mode",
			fn: func() error {
				_, err := menukit.GetMenuData(
					ctx,
					menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
					menukit.WithToken("your-auth-token"),
					menukit.WithStoreID(12345),
					menukit.WithChannelCode("POS"),
					menukit.WithMode(menukit.ModeAutoMerge),
					menukit.WithProdDir("./perf/prod"),
					menukit.WithTempDir("./perf/temp"),
				)
				return err
			},
		},
		{
			name: "Production only mode",
			fn: func() error {
				_, err := menukit.GetMenuDataFromProd(
					ctx,
					menukit.WithProdDir("./perf/prod"),
					menukit.WithStoreID(12345),
					menukit.WithChannelCode("POS"),
				)
				return err
			},
		},
		{
			name: "Fetch to temp",
			fn: func() error {
				return menukit.FetchToTemp(
					ctx,
					menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
					menukit.WithToken("your-auth-token"),
					menukit.WithStoreID(12345),
					menukit.WithChannelCode("POS"),
					menukit.WithTempDir("./perf/temp"),
					menukit.WithProdDir("./perf/prod"),
				)
			},
		},
		{
			name: "Merge temp to prod",
			fn: func() error {
				return menukit.MergeTempToProd(
					ctx,
					menukit.WithProdDir("./perf/prod"),
					menukit.WithTempDir("./perf/temp"),
				)
			},
		},
	}

	for _, op := range operations {
		start := time.Now()
		err := op.fn()
		duration := time.Since(start)
		
		if err != nil {
			fmt.Printf("  %s: FAILED (%v) in %v\n", op.name, err, duration)
		} else {
			fmt.Printf("  %s: SUCCESS in %v\n", op.name, duration)
		}
	}
}

// multiStoreExample demonstrates handling multiple stores
func multiStoreExample() {
	ctx := context.Background()

	stores := []struct {
		id      uint64
		name    string
		channel string
	}{
		{12345, "Store A", "POS"},
		{12346, "Store B", "KIOSK"},
		{12347, "Store C", "MOBILE"},
	}

	for _, store := range stores {
		fmt.Printf("  Processing %s (ID: %d, Channel: %s)\n", store.name, store.id, store.channel)
		
		start := time.Now()
		menuData, err := menukit.GetMenuData(
			ctx,
			menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
			menukit.WithToken("your-auth-token"),
			menukit.WithStoreID(store.id),
			menukit.WithChannelCode(store.channel),
			menukit.WithProdDir(fmt.Sprintf("./multi_store/%d/prod", store.id)),
			menukit.WithTempDir(fmt.Sprintf("./multi_store/%d/temp", store.id)),
			menukit.WithMaxRetries(2), // Reduced retries for multi-store
			menukit.WithRetryDelay(1*time.Second),
		)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("    FAILED: %v (took %v)\n", err, duration)
		} else {
			fmt.Printf("    SUCCESS: %d categories (took %v)\n", len(menuData.Payload.Category), duration)
		}
	}
}

// dataValidationExample demonstrates data validation
func dataValidationExample() {
	ctx := context.Background()

	// Get menu data
	menuData, err := menukit.GetMenuData(
		ctx,
		menukit.WithHost("https://hipos-saas-qa.hexcloud.cn"),
		menukit.WithToken("your-auth-token"),
		menukit.WithStoreID(12345),
		menukit.WithChannelCode("POS"),
		menukit.WithProdDir("./validation/prod"),
		menukit.WithNotFilterByPrice(false), // Enable price filtering
	)

	if err != nil {
		fmt.Printf("  Data fetch failed: %v\n", err)
		return
	}

	// Validate the data
	if menuData == nil || menuData.Payload == nil {
		fmt.Println("  VALIDATION FAILED: No payload data")
		return
	}

	fmt.Printf("  Menu validation:\n")
	fmt.Printf("    Menu ID: %s\n", menuData.Payload.MenuId)
	fmt.Printf("    Categories: %d\n", len(menuData.Payload.Category))

	totalProducts := 0
	for _, category := range menuData.Payload.Category {
		totalProducts += len(category.ProductList)
	}
	fmt.Printf("    Total products: %d\n", totalProducts)

	if totalProducts == 0 {
		fmt.Println("  WARNING: No products found in menu")
	} else {
		fmt.Println("  VALIDATION PASSED: Menu data looks good")
	}
}
