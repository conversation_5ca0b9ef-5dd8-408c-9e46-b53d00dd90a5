package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"
	"hexcloud.cn/hicloud/menukit"
)

func main() {
	// Command line flags
	var (
		host        = flag.String("host", "https://hipos-saas-qa.hexcloud.cn", "API host")
		token       = flag.String("token", "", "Authentication token")
		storeID     = flag.String("store-id", "", "Store ID")
		channel     = flag.String("channel", "POS", "Channel code")
		lang        = flag.String("lang", "zh-CN", "Language")
		partnerID   = flag.String("partner-id", "", "Partner ID")
		prodDir     = flag.String("prod-dir", "./prod", "Production directory")
		tempDir     = flag.String("temp-dir", "./temp", "Temporary directory")
		mode        = flag.String("mode", "auto", "Operation mode: auto, fetch, merge, prod")
		maxRetries  = flag.Int("max-retries", 3, "Maximum retry attempts")
		retryDelay  = flag.Duration("retry-delay", 500*time.Millisecond, "Retry delay")
		showHelp    = flag.Bool("help", false, "Show help")
		verbose     = flag.Bool("verbose", false, "Verbose output")
	)

	flag.Parse()

	if *showHelp {
		showUsage()
		return
	}

	// Validate required parameters
	if *token == "" {
		log.Fatal("Error: token is required")
	}
	if *storeID == "" {
		log.Fatal("Error: store-id is required")
	}

	// Convert store ID to uint64
	storeIDUint, err := strconv.ParseUint(*storeID, 10, 64)
	if err != nil {
		log.Fatalf("Error: invalid store-id: %v", err)
	}

	ctx := context.Background()

	if *verbose {
		fmt.Printf("Configuration:\n")
		fmt.Printf("  Host: %s\n", *host)
		fmt.Printf("  Store ID: %d\n", storeIDUint)
		fmt.Printf("  Channel: %s\n", *channel)
		fmt.Printf("  Language: %s\n", *lang)
		fmt.Printf("  Partner ID: %s\n", *partnerID)
		fmt.Printf("  Production Dir: %s\n", *prodDir)
		fmt.Printf("  Temporary Dir: %s\n", *tempDir)
		fmt.Printf("  Mode: %s\n", *mode)
		fmt.Printf("  Max Retries: %d\n", *maxRetries)
		fmt.Printf("  Retry Delay: %v\n", *retryDelay)
		fmt.Println()
	}

	// Common options
	options := []menukit.Option{
		menukit.WithHost(*host),
		menukit.WithToken(*token),
		menukit.WithStoreID(storeIDUint),
		menukit.WithChannelCode(*channel),
		menukit.WithLang(*lang),
		menukit.WithPartnerID(*partnerID),
		menukit.WithProdDir(*prodDir),
		menukit.WithTempDir(*tempDir),
		menukit.WithMaxRetries(*maxRetries),
		menukit.WithRetryDelay(*retryDelay),
	}

	// Execute based on mode
	switch *mode {
	case "auto":
		executeAutoMode(ctx, options, *verbose)
	case "fetch":
		executeFetchMode(ctx, options, *verbose)
	case "merge":
		executeMergeMode(ctx, options, *verbose)
	case "prod":
		executeProdMode(ctx, options, *verbose)
	case "check":
		executeCheckMode(options, *verbose)
	default:
		log.Fatalf("Error: unknown mode '%s'. Use auto, fetch, merge, prod, or check", *mode)
	}
}

func showUsage() {
	fmt.Println("MenuKit Simple CLI")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  simple_cli [options]")
	fmt.Println()
	fmt.Println("Options:")
	flag.PrintDefaults()
	fmt.Println()
	fmt.Println("Modes:")
	fmt.Println("  auto  - Auto fetch and merge (default)")
	fmt.Println("  fetch - Fetch data to temporary directory only")
	fmt.Println("  merge - Merge temporary data to production")
	fmt.Println("  prod  - Load data from production directory only")
	fmt.Println("  check - Check for updates in temporary directory")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  simple_cli -token=your-token -store-id=12345")
	fmt.Println("  simple_cli -token=your-token -store-id=12345 -mode=fetch")
	fmt.Println("  simple_cli -token=your-token -store-id=12345 -mode=check")
}

func executeAutoMode(ctx context.Context, options []menukit.Option, verbose bool) {
	if verbose {
		fmt.Println("Executing auto mode (fetch and merge)...")
	}

	start := time.Now()
	menuData, err := menukit.GetMenuData(ctx, options...)
	duration := time.Since(start)

	if err != nil {
		log.Fatalf("Auto mode failed: %v", err)
	}

	fmt.Printf("Success! Fetched menu data in %v\n", duration)
	if menuData.Payload != nil {
		fmt.Printf("Categories: %d\n", len(menuData.Payload.Category))
		
		totalProducts := 0
		for _, category := range menuData.Payload.Category {
			totalProducts += len(category.ProductList)
		}
		fmt.Printf("Total products: %d\n", totalProducts)
		
		if verbose {
			fmt.Printf("Menu ID: %s\n", menuData.Payload.MenuId)
			fmt.Printf("Menu Name: %s\n", menuData.Payload.Name)
			fmt.Printf("Update Time: %s\n", menuData.Payload.UpdateTime)
			fmt.Printf("Version: %d\n", menuData.Payload.Version)
		}
	}
}

func executeFetchMode(ctx context.Context, options []menukit.Option, verbose bool) {
	if verbose {
		fmt.Println("Executing fetch mode (fetch to temporary directory)...")
	}

	start := time.Now()
	err := menukit.FetchToTemp(ctx, options...)
	duration := time.Since(start)

	if err != nil {
		log.Fatalf("Fetch mode failed: %v", err)
	}

	fmt.Printf("Success! Fetched data to temporary directory in %v\n", duration)
}

func executeMergeMode(ctx context.Context, options []menukit.Option, verbose bool) {
	if verbose {
		fmt.Println("Executing merge mode (merge temporary to production)...")
	}

	start := time.Now()
	err := menukit.MergeTempToProd(ctx, options...)
	duration := time.Since(start)

	if err != nil {
		log.Fatalf("Merge mode failed: %v", err)
	}

	fmt.Printf("Success! Merged temporary data to production in %v\n", duration)
}

func executeProdMode(ctx context.Context, options []menukit.Option, verbose bool) {
	if verbose {
		fmt.Println("Executing production mode (load from production directory)...")
	}

	start := time.Now()
	menuData, err := menukit.GetMenuDataFromProd(ctx, options...)
	duration := time.Since(start)

	if err != nil {
		log.Fatalf("Production mode failed: %v", err)
	}

	fmt.Printf("Success! Loaded menu data from production in %v\n", duration)
	if menuData.Payload != nil {
		fmt.Printf("Categories: %d\n", len(menuData.Payload.Category))
		
		totalProducts := 0
		for _, category := range menuData.Payload.Category {
			totalProducts += len(category.ProductList)
		}
		fmt.Printf("Total products: %d\n", totalProducts)
	}
}

func executeCheckMode(options []menukit.Option, verbose bool) {
	if verbose {
		fmt.Println("Checking for updates in temporary directory...")
	}

	hasUpdate, err := menukit.HasTempUpdate(options...)
	if err != nil {
		log.Fatalf("Check mode failed: %v", err)
	}

	if hasUpdate {
		fmt.Println("Updates available in temporary directory")
		os.Exit(1) // Exit with code 1 to indicate updates available
	} else {
		fmt.Println("No updates in temporary directory")
		os.Exit(0) // Exit with code 0 to indicate no updates
	}
}
