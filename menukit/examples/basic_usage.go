package main

import (
	"context"
	"fmt"
	"log"
	"time"
	"hexcloud.cn/hicloud/menukit"
)

func main() {
	// Example usage of MenuKit
	ctx := context.Background()

	// Configuration
	host := "https://hipos-saas-qa.hexcloud.cn"
	token := "your-auth-token"
	storeID := uint64(12345)
	channelCode := "POS"
	lang := "zh-CN"
	partnerID := "partner123"

	fmt.Println("=== MenuKit Complete Usage Examples ===")

	// 1. Auto fetch and merge (default mode) - Recommended for most use cases
	fmt.Println("\n1. Auto fetch and merge mode:")
	menuData, err := menukit.GetMenuData(
		ctx,
		menukit.WithHost(host),
		menukit.WithToken(token),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
		menukit.WithLang(lang),
		menukit.WithPartnerID(partnerID),
		menukit.WithProdDir("./data/prod"),
		menukit.WithTempDir("./data/temp"),
		menukit.WithMaxRetries(3),
		menukit.WithRetryDelay(1*time.Second),
		menukit.WithExpireInterval(24*time.Hour),
	)
	if err != nil {
		log.Printf("Failed to get menu data: %v", err)
		// In production, you might want to continue with other operations
	} else {
		fmt.Printf("Successfully fetched menu data with %d categories\n", len(menuData.Payload.Category))

		// Display some basic information about the menu
		if menuData.Payload != nil {
			fmt.Printf("Menu ID: %s\n", menuData.Payload.MenuId)
			fmt.Printf("Menu Name: %s\n", menuData.Payload.Name)
			fmt.Printf("Update Time: %s\n", menuData.Payload.UpdateTime)
			fmt.Printf("Version: %d\n", menuData.Payload.Version)
		}
	}

	// 2. Fetch to temporary directory only - Useful for staged deployments
	fmt.Println("\n2. Fetch to temporary directory only:")
	err = menukit.FetchToTemp(
		ctx,
		menukit.WithHost(host),
		menukit.WithToken(token),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
		menukit.WithLang(lang),
		menukit.WithPartnerID(partnerID),
		menukit.WithTempDir("./data/temp"),
		menukit.WithProdDir("./data/prod"), // Needed for MD5 comparison
	)
	if err != nil {
		log.Printf("Failed to fetch to temp: %v", err)
	} else {
		fmt.Println("Successfully fetched data to temp directory")
	}

	// 3. Check if there are temporary updates
	fmt.Println("\n3. Checking for temporary updates:")
	hasUpdate, err := menukit.HasTempUpdate(
		menukit.WithProdDir("./data/prod"),
		menukit.WithTempDir("./data/temp"),
	)
	if err != nil {
		log.Printf("Failed to check for updates: %v", err)
	} else {
		if hasUpdate {
			fmt.Println("There are updates in the temp directory")

			// 4. Merge temporary data to production data
			fmt.Println("\n4. Merging temporary data to production:")
			err = menukit.MergeTempToProd(
				ctx,
				menukit.WithProdDir("./data/prod"),
				menukit.WithTempDir("./data/temp"),
			)
			if err != nil {
				log.Printf("Failed to merge temp to prod: %v", err)
			} else {
				fmt.Println("Successfully merged temp to prod")
			}
		} else {
			fmt.Println("No updates in the temp directory")
		}
	}

	// 5. Load data from production directory only - Useful for read-only scenarios
	fmt.Println("\n5. Loading data from production directory only:")
	prodMenuData, err := menukit.GetMenuDataFromProd(
		ctx,
		menukit.WithProdDir("./data/prod"),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
	)
	if err != nil {
		log.Printf("Failed to get menu data from prod: %v", err)
	} else {
		fmt.Printf("Successfully loaded menu data from prod with %d categories\n", len(prodMenuData.Payload.Category))
	}

	// 6. Advanced configuration example
	fmt.Println("\n6. Advanced configuration example:")
	advancedMenuData, err := menukit.GetMenuData(
		ctx,
		menukit.WithHost(host),
		menukit.WithToken(token),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
		menukit.WithLang(lang),
		menukit.WithPartnerID(partnerID),
		menukit.WithProdDir("./data/advanced/prod"),
		menukit.WithTempDir("./data/advanced/temp"),
		menukit.WithMode(menukit.ModeAutoMerge),
		menukit.WithMaxRetries(5),
		menukit.WithRetryDelay(2*time.Second),
		menukit.WithExpireInterval(12*time.Hour),
		menukit.WithNotFilterByPrice(false),
	)
	if err != nil {
		log.Printf("Failed to get advanced menu data: %v", err)
	} else {
		fmt.Printf("Successfully fetched advanced menu data with %d categories\n", len(advancedMenuData.Payload.Category))
	}

	// 7. Different operation modes
	fmt.Println("\n7. Different operation modes:")

	// Mode: Fetch only
	fmt.Println("  - Fetch only mode:")
	_, err = menukit.GetMenuData(
		ctx,
		menukit.WithHost(host),
		menukit.WithToken(token),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
		menukit.WithMode(menukit.ModeFetchOnly),
		menukit.WithTempDir("./data/modes/temp"),
	)
	if err != nil {
		log.Printf("    Fetch only mode failed: %v", err)
	} else {
		fmt.Println("    Fetch only mode completed successfully")
	}

	// Mode: Production only
	fmt.Println("  - Production only mode:")
	_, err = menukit.GetMenuData(
		ctx,
		menukit.WithMode(menukit.ModeProdOnly),
		menukit.WithProdDir("./data/modes/prod"),
		menukit.WithStoreID(storeID),
		menukit.WithChannelCode(channelCode),
	)
	if err != nil {
		log.Printf("    Production only mode failed: %v", err)
	} else {
		fmt.Println("    Production only mode completed successfully")
	}

	fmt.Println("\n=== MenuKit Examples Completed ===")
}