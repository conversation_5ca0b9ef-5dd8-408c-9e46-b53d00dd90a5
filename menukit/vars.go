package menukit

import (
	"net/http"
	"time"
	"hexcloud.cn/hicloud/menukit/utils"
)

const (
	// DefaultHost is the default API host
	DefaultHost = "https://hipos-saas-qa.hexcloud.cn"

	// API endpoints
	DefaultChannelProductListApi = "/api/omnichannel/channel/product/list/v2"
	DefaultChannelProductInfoApi = "/api/omnichannel/channel/product/productInfo/list"
	DefaultChannelProductAttrApi = "/api/omnichannel/channel/product/attr/list"
	DefaultPriceApi              = "/api/v1/price-center/get-product-price-full-info-agent"
	DefaultBaseDataAPi           = "/api/menu-center-pos/menu/queryPosMenuProduct"

	// File names
	DefaultPriceFileName       = "price.json"
	DefaultProductListFileName = "product_list.json"
	DefaultProductInfoFileName = "product_info.json"
	DefaultProductAttrFileName = "product_attr.json"
	DefaultPriceTypeFileName   = "price_type.json"

	// Price filtering constants
	PriceUnavailable = float64(-9999)
	FLEX_SET         = "FLEX_SET"
)

// GlobalHTTPClient is the global HTTP client for connection reuse
var GlobalHTTPClient = &http.Client{
	Timeout: 30 * time.Second,
}

// FormatDataSize converts bytes to a more readable format
func FormatDataSize(bytes int) string {
	return utils.FormatDataSize(bytes)
}