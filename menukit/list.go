package menukit

import (
	"context"
	"errors"
	"github.com/spf13/cast"
	"hexcloud.cn/hicloud/menukit/models"
)

// ProductListOpt represents product list options
type ProductListOpt struct {
	ProductList             []*models.Category
	ProductInfo             []*models.Product
	ProductAttr             []*models.AdditionalAttribute
	PriceInfo               *models.PriceInfo
	PriceTypeInfo           []*models.PriceType
	OriginProductListMd5    *string
	OriginProductSpuInfoMd5 *string
	OriginAttrListMd5       *string
	OriginPriceTypeMd5      *string
}

func getChannelProductList(ctx context.Context, channelCode string, opts *ProductListOpt) (*models.ProductListResponse, error) {
	productListArray := opts.ProductList

	productInfoMap, err := getProductInfoMap(opts.ProductInfo)
	if err != nil {
		return nil, errors.New("get product info map error: " + err.Error())
	}

	productAttrMap, err := getProductAttr(opts.ProductAttr)
	if err != nil {
		return nil, errors.New("get product attr map error: " + err.Error())
	}

	productAttrValuesMap, err := getProductAttrValues(opts.ProductAttr)
	if err != nil {
		return nil, errors.New("get product attr values map error: " + err.Error())
	}

	priceInfoMap, err := getPriceInfoMap(opts.PriceInfo)
	if err != nil {
		return nil, errors.New("get price info map error: " + err.Error())
	}

	priceTypeMap, channelMap, err := getPriceTypeMapAndChannelMap(opts.PriceTypeInfo)
	if err != nil {
		return nil, errors.New("get price type info and channel map error: " + err.Error())
	}
	resp := &models.ProductListResponse{Payload: &models.Payload{Category: make([]*models.Category, 0, len(productListArray))}}

	for _, category := range productListArray {
		productList := category.ProductList

		filterProductList := make([]*models.Product, 0, len(productList))
		for _, product := range productList {
			// Set product information
			if err := setProductInfo(product, productInfoMap); err != nil {
				return nil, errors.Join(errors.New("set product info error: "), err)
			}

			// Set product attributes
			if err := setProductAttr(product, productAttrMap); err != nil {
				return nil, err
			}

			// Supplement attribute_values
			if err := setAttrValues(product, productAttrValuesMap); err != nil {
				return nil, errors.Join(errors.New("set attr values error: "), err)
			}

			// Supplement subItemSpu
			// productList-->>relation-->>set-->>spu_id (locate corresponding product in product list interface by product spu_id, "product_id" to supplement product information),
			// placed in set in new field:"subItemSpu":{}, put product information into it.
			if err := setSubItemSpu(product, productInfoMap, productAttrMap, productAttrValuesMap); err != nil {
				return nil, errors.Join(errors.New("set sub item spu error: "), err)
			}

			// Set relation addition
			// productList-->>relation-->>set-->>addition(if addition is not empty, move and replace subItemSpu-->>relation-->>addition), delete addition
			if err := setRelationAddition(product, productInfoMap); err != nil {
				return nil, errors.Join(errors.New("set relation addition error: "), err)
			}

			// Filter and fill price information
			priceTypeID, ok := channelMap[channelCode]
			if !ok {
				return nil, errors.New("channel code not found")
			}

			product, err = filterAndFillPriceInfo(product, priceInfoMap, priceTypeMap, priceTypeID)
			if err != nil {
				return nil, errors.Join(errors.New("filter and fill price info error: "), err)
			}
			if product == nil {
				continue
			}
			filterProductList = append(filterProductList, product)
		}
		category.ProductList = filterProductList
		if len(filterProductList) > 0 {
			resp.Payload.Category = append(resp.Payload.Category, category)
		}
	}
	return resp, nil
}

func getProductInfoMap(productInfo []*models.Product) (map[string]*models.Product, error) {
	productSetInfoMap := make(map[string]*models.Product, len(productInfo))
	for _, info := range productInfo {
		productSetInfoMap[info.ProductId] = info
	}
	return productSetInfoMap, nil
}

func getProductAttr(productAttr []*models.AdditionalAttribute) (map[string]*models.AdditionalAttribute, error) {
	productAttrMap := make(map[string]*models.AdditionalAttribute, len(productAttr))
	for _, attr := range productAttr {
		productAttrMap[attr.Id] = attr
	}
	return productAttrMap, nil
}

func getProductAttrValues(productAttr []*models.AdditionalAttribute) (map[string]*models.AttributeValues, error) {
	productAttrValuesMap := make(map[string]*models.AttributeValues, len(productAttr))
	for _, attr := range productAttr {
		for _, attrValue := range attr.AttributeValues {
			productAttrValuesMap[attrValue.Id] = attrValue
		}
	}
	return productAttrValuesMap, nil
}

func setProductInfo(product *models.Product, productSetInfoMap map[string]*models.Product) error {
	info, ok := productSetInfoMap[product.ProductId]
	if !ok {
		return nil
	}
	if info.Relation != nil {
		// Don't use unmarshal, ensure efficiency
		if product.Relation == nil {
			product.Relation = info.Relation
		} else {
			// Enumerate fields
			if len(info.Relation.ProductLabel) != 0 {
				product.Relation.ProductLabel = info.Relation.ProductLabel
			}
			if len(info.Relation.AdditionalAttribute) != 0 {
				product.Relation.AdditionalAttribute = info.Relation.AdditionalAttribute
			}
			if len(info.Relation.SecondLevelAttrList) != 0 {
				product.Relation.SecondLevelAttrList = info.Relation.SecondLevelAttrList
			}
			if len(info.Relation.Set) != 0 {
				product.Relation.Set = info.Relation.Set
			}
			if len(info.Relation.Addition) != 0 {
				product.Relation.Addition = info.Relation.Addition
			}
			if len(info.Relation.Sku) != 0 {
				product.Relation.Sku = info.Relation.Sku
			}
			if info.Relation.ProductCategory != "" {
				product.Relation.ProductCategory = info.Relation.ProductCategory
			}
			if info.Relation.Unit != "" {
				product.Relation.Unit = info.Relation.Unit
			}
		}
	}
	mergeProductInfo(info, product)
	return nil
}

func setAttrValues(product *models.Product, productAttrValuesMap map[string]*models.AttributeValues) error {
	if product.Relation == nil {
		return nil
	}
	for _, attr := range product.Relation.AdditionalAttribute {
		for _, attrValue := range attr.AttributeValues {
			attrValueInfo, ok := productAttrValuesMap[attrValue.Id]
			if !ok {
				continue
			}
			mergeAttributeValues(attrValue, attrValueInfo)
		}
	}
	return nil
}

// setSubItemSpu supplements subItemSpu
// 1. Extract additional_attribute from relation in set-->subItemSpu-->relation, then supplement sub-product attributes
// 2. productList-->>relation-->>set-->>spu_id (locate corresponding product in product list interface by product spu_id, "product_id" to supplement product information),
// placed in set in new field:"subItemSpu":{}, put product information into it.
// 3. productList-->>relation-->>set-->>raisePrice assign to: subItemSpu-->>relation-->>sku-->>selling_price;
// 4. productList-->>relation-->>set-->>subItemSpu-->>relation-->>sku-->>sellingPrice assign to: subItemSpu-->>relation-->>sku
// -->original_price
func setSubItemSpu(product *models.Product,
	productInfoMap map[string]*models.Product,
	productAttrMap map[string]*models.AdditionalAttribute,
	productAttrValuesMap map[string]*models.AttributeValues) error {
	if product.Relation == nil {
		return nil
	}

	for _, setItem := range product.Relation.Set {
		spuInfo, ok := productInfoMap[setItem.SpuId]
		if !ok {
			continue
		}

		raisePrice := setItem.RaisePrice

		// Set subItemSpu->relation->sku->sellingPrice to subItemSpu->relation->sku->original_price
		if spuInfo.Relation != nil {
			for _, skuItem := range spuInfo.Relation.Sku {
				// Set raisePrice to subItemSpu->relation->sku->selling_price
				skuItem.SellingPrice = raisePrice

				// Set subItemSpu->relation->sku->sellingPrice to subItemSpu->relation->sku->original_price
				skuItem.OriginalPrice = skuItem.SellingPrice
			}
		}

		if setItem.SubItemSpu != nil {
			if setItem.SubItemSpu.Relation != nil {
				if spuInfo.Relation != nil {
					spuInfo.Relation.AdditionalAttribute = setItem.SubItemSpu.Relation.AdditionalAttribute
				} else {
					spuInfo.Relation = &models.Relation{
						AdditionalAttribute: setItem.SubItemSpu.Relation.AdditionalAttribute,
					}
				}
			}
		}
	}
	return nil
}

func setRelationAddition(product *models.Product, productInfoMap map[string]*models.Product) error {
	if product.Relation == nil {
		return nil
	}

	relation := product.Relation

	for _, setItem := range relation.Set {
		setItem.SubItemSpu.Relation.Addition = setItem.Addition
		setItem.Addition = nil
	}

	return nil
}

func setProductAttr(product *models.Product, productAttrMap map[string]*models.AdditionalAttribute) error {
	if product.Relation == nil {
		return nil
	}

	for _, attr := range product.Relation.AdditionalAttribute {
		attrInfo, ok := productAttrMap[attr.Id]
		if !ok {
			continue
		}
		mergeAdditionalAttribute(attr, attrInfo)
	}
	return nil
}

func mergeMaps(map1, map2 map[string]interface{}) map[string]interface{} {
	for k, v := range map2 {
		if map1[k] == nil || map1[k] == "" {
			map1[k] = v
		}
	}
	return map1
}

func getPriceInfoMap(priceInfo *models.PriceInfo) (map[string]map[string]*models.PriceAgent, error) {
	priceMap := make(map[string]map[string]*models.PriceAgent, len(priceInfo.Rows))

	for _, info := range priceInfo.Rows {
		if !info.IsValid {
			continue
		}
		priceTypeID := cast.ToString(info.PriceTypeId)
		if priceTypePriceMap, ok := priceMap[info.ItemCode]; ok {
			priceTypePriceMap[priceTypeID] = info
		} else {
			priceMap[info.ItemCode] = map[string]*models.PriceAgent{priceTypeID: info}
		}
	}
	return priceMap, nil
}

// {
//    "code": "UPA1",
//    "name": "UPA1",
//    "channel_code_map": "POS",
//    "channel_code_list": [
//        "POS",
//        "POS_H5",
//        "POS_APP"
//    ]
// }
//
// Return priceTypeMap: code->id channelMap: channel_code->id
func getPriceTypeMapAndChannelMap(priceTypeInfo []*models.PriceType) (map[string]string, map[string]string, error) {
	priceTypeMap := make(map[string]string, len(priceTypeInfo))
	channelMap := make(map[string]string)
	for _, info := range priceTypeInfo {
		if info.Fields == nil {
			continue
		}

		id := cast.ToString(info.ID)
		code := cast.ToString(info.Fields.Code)
		priceTypeMap[id] = code

		for _, channelCode := range info.Fields.ChannelCodeList {
			channelMap[channelCode] = id
		}
	}
	return priceTypeMap, channelMap, nil
}

// filterAndFillPriceInfo is a placeholder function - actual implementation would filter and fill price info
func filterAndFillPriceInfo(product *models.Product, priceInfoMap map[string]map[string]*models.PriceAgent, priceTypeMap map[string]string, priceTypeID string) (*models.Product, error) {
	// Placeholder implementation - in a real implementation, this would:
	// 1. Look up the product's price in priceInfoMap based on product code and priceTypeID
	// 2. Filter out products with invalid prices
	// 3. Fill in price information in the product object
	return product, nil
}

func mergeProductInfo(info *models.Product, product *models.Product) {
	if isInvalidStr(product.Name) {
		product.Name = info.Name
	}
	if isNilOrEmpty(product.NameLan) && !isNilOrEmpty(info.NameLan) {
		product.NameLan = info.NameLan
	}
	if isNilOrEmpty(product.MnemonicCode) && !isNilOrEmpty(info.MnemonicCode) {
		product.MnemonicCode = info.MnemonicCode
	}
	if isNilOrEmpty(product.DealId) && !isNilOrEmpty(info.DealId) {
		product.DealId = info.DealId
	}
	if isNilOrEmpty(product.FirstPinyin) && !isNilOrEmpty(info.FirstPinyin) {
		product.FirstPinyin = info.FirstPinyin
	}
	if isNilOrEmpty(product.ProductCategory) && !isNilOrEmpty(info.ProductCategory) {
		product.ProductCategory = info.ProductCategory
	}
	if isInvalidStr(product.ProductId) {
		product.ProductId = info.ProductId
	}
	if isNilOrEmpty(product.Upc) && !isNilOrEmpty(info.Upc) {
		product.Upc = info.Upc
	}
	if isInvalidStr(product.SellingPrice) {
		product.SellingPrice = info.SellingPrice
	}
	if isInvalidStr(product.OriginalPrice) {
		product.OriginalPrice = info.OriginalPrice
	}
	if isInvalidStr(product.Code) {
		product.Code = info.Code
	}
	if isInvalidStr(product.PriceType) {
		product.PriceType = info.PriceType
	}
	if isInvalidStr(product.SaleType) {
		product.SaleType = info.SaleType
	}
	if isInvalidStr(product.Unit) {
		product.Unit = info.Unit
	}
	if !product.IsJoinQueue && info.IsJoinQueue {
		product.IsJoinQueue = true
	}
	if product.Relation == nil && info.Relation != nil {
		product.Relation = info.Relation
	}
	if isInvalidStr(product.ChannelAvailablePeriod) {
		product.ChannelAvailablePeriod = info.ChannelAvailablePeriod
	}
	if isInvalidStr(product.ChannelAvailablePeriodType) {
		product.ChannelAvailablePeriodType = info.ChannelAvailablePeriodType
	}
	if isInvalidStr(product.ChannelDescription) {
		product.ChannelDescription = info.ChannelDescription
	}
	if isNilOrEmpty(product.ChannelDescriptionLan) && !isNilOrEmpty(info.ChannelDescriptionLan) {
		product.ChannelDescriptionLan = info.ChannelDescriptionLan
	}
	if isInvalidStr(product.AppChannelDescription) {
		product.AppChannelDescription = info.AppChannelDescription
	}
	if isInvalidStr(product.ChannelRemark) {
		product.ChannelRemark = info.ChannelRemark
	}
	if isInvalidStr(product.Picture) {
		product.Picture = info.Picture
	}
	if product.ChannelSimpleImage == nil && info.ChannelSimpleImage != nil {
		product.ChannelSimpleImage = info.ChannelSimpleImage
	}
	if product.ChannelPicture == nil && info.ChannelPicture != nil {
		product.ChannelPicture = info.ChannelPicture
	}
	if isInvalidStr(product.PictureDisplaySize) {
		product.PictureDisplaySize = info.PictureDisplaySize
	}
	if isInvalidStr(product.SetType) {
		product.SetType = info.SetType
	}
	if isInvalidStr(product.Status) {
		product.Status = info.Status
	}
	if product.ChannelTag == nil && info.ChannelTag != nil {
		product.ChannelTag = info.ChannelTag
	}
	if product.ChannelPackageFixedFee == 0 && info.ChannelPackageFixedFee != 0 {
		product.ChannelPackageFixedFee = info.ChannelPackageFixedFee
	}
	if !product.ChannelHasCupLabelNotice && info.ChannelHasCupLabelNotice {
		product.ChannelHasCupLabelNotice = true
	}
	if isInvalidStr(product.ChannelCupLabelNotice) {
		product.ChannelCupLabelNotice = info.ChannelCupLabelNotice
	}
	if isInvalidStr(product.MakeTime) {
		product.MakeTime = info.MakeTime
	}
	if !product.ChannelNoCupCounted && info.ChannelNoCupCounted {
		product.ChannelNoCupCounted = true
	}
	if !product.ChannelHasCupCounted && info.ChannelHasCupCounted {
		product.ChannelHasCupCounted = true
	}
	if product.SellSpecification == nil && info.SellSpecification != nil {
		product.SellSpecification = info.SellSpecification
	}
	if !product.IsAdditionalAttribute && info.IsAdditionalAttribute {
		product.IsAdditionalAttribute = true
	}
	if !product.IsAddition && info.IsAddition {
		product.IsAddition = true
	}
	if !product.ProductShelfStatus && info.ProductShelfStatus {
		product.ProductShelfStatus = true
	}
	if !product.WeightProduct && info.WeightProduct {
		product.WeightProduct = true
	}
	if product.Stock == nil && info.Stock != nil {
		product.Stock = info.Stock
	}
	if !product.StockOperate && info.StockOperate {
		product.StockOperate = true
	}
	if isInvalidStr(product.ProductPrintName) {
		product.ProductPrintName = info.ProductPrintName
	}
	if isNilOrEmpty(product.ProductPrintNameLan) && !isNilOrEmpty(info.ProductPrintNameLan) {
		product.ProductPrintNameLan = info.ProductPrintNameLan
	}
	if isInvalidStr(product.ShowColor) {
		product.ShowColor = info.ShowColor
	}
	if isInvalidStr(product.Sort) {
		product.Sort = info.Sort
	}
	if isInvalidStr(product.ServingSize) {
		product.ServingSize = info.ServingSize
	}
	if !product.IsCupNotice && info.IsCupNotice {
		product.IsCupNotice = true
	}
	if product.ChannelMinBuyCount == 0 && info.ChannelMinBuyCount > 0 {
		product.ChannelMinBuyCount = info.ChannelMinBuyCount
	}
	if !product.SplitCharging && info.SplitCharging {
		product.SplitCharging = true
	}
	if !product.MergeTopping && info.MergeTopping {
		product.MergeTopping = true
	}
	if product.DifferentChannelAvailablePeriod == nil && info.DifferentChannelAvailablePeriod != nil {
		product.DifferentChannelAvailablePeriod = info.DifferentChannelAvailablePeriod
	}
	if isNilOrEmpty(product.ChannelSupportTag) && !isNilOrEmpty(info.ChannelSupportTag) {
		product.ChannelSupportTag = info.ChannelSupportTag
	}
	if product.BowlCount == 0 && info.BowlCount > 0 {
		product.BowlCount = info.BowlCount
	}
	if isInvalidStr(product.RadioBowOptions) {
		product.RadioBowOptions = info.RadioBowOptions
	}
	if isInvalidStr(product.MenuCategoryId) {
		product.MenuCategoryId = info.MenuCategoryId
	}
	if isInvalidStr(product.MenuCategoryName) {
		product.MenuCategoryName = info.MenuCategoryName
	}
	if isInvalidStr(product.MenuCategoryPicture) {
		product.MenuCategoryPicture = info.MenuCategoryPicture
	}
	if isNilOrEmptyMap(product.ChannelItemPriceMap) && !isNilOrEmptyMap(info.ChannelItemPriceMap) {
		product.ChannelItemPriceMap = info.ChannelItemPriceMap
	}
	if isNilOrEmpty(product.ItemGroupList) && !isNilOrEmpty(info.ItemGroupList) {
		product.ItemGroupList = info.ItemGroupList
	}
	if isNilOrEmpty(product.ItemGroupPosList) && !isNilOrEmpty(info.ItemGroupPosList) {
		product.ItemGroupPosList = info.ItemGroupPosList
	}
	if product.AutoLinkedDiningMethodProducts == nil && info.AutoLinkedDiningMethodProducts != nil {
		product.AutoLinkedDiningMethodProducts = info.AutoLinkedDiningMethodProducts
	}
	if isNilOrEmpty(product.RecommendGroupList) && !isNilOrEmpty(info.RecommendGroupList) {
		product.RecommendGroupList = info.RecommendGroupList
	}
	if isInvalidStr(product.BasicSetSubitemId) {
		product.BasicSetSubitemId = info.BasicSetSubitemId
	}
	if isInvalidStr(product.ExtCode) {
		product.ExtCode = info.ExtCode
	}
	if isInvalidStr(product.ShowType) {
		product.ShowType = info.ShowType
	}
	if isInvalidStr(product.FeedLabel) {
		product.FeedLabel = info.FeedLabel
	}
	if isInvalidStr(product.Currency) {
		product.Currency = info.Currency
	}
	if isNilOrEmpty(product.ChannelLabel) && !isNilOrEmpty(info.ChannelLabel) {
		product.ChannelLabel = info.ChannelLabel
	}
	if !product.AdultStatus && info.AdultStatus {
		product.AdultStatus = true
	}
	if isInvalidStr(product.AdultNotice) {
		product.AdultNotice = info.AdultNotice
	}
	if product.RecommendStatus == nil && info.RecommendStatus != nil {
		product.RecommendStatus = info.RecommendStatus
	}
	if product.ShowPriceStatus == nil && info.ShowPriceStatus != nil {
		product.ShowPriceStatus = info.ShowPriceStatus
	}
	if product.PriceUp == nil && info.PriceUp != nil {
		product.PriceUp = info.PriceUp
	}
	if product.InvisibleStatus == nil && info.InvisibleStatus != nil {
		product.InvisibleStatus = info.InvisibleStatus
	}
	if isInvalidStr(product.DisplayMode) {
		product.DisplayMode = info.DisplayMode
	}
	if isNilOrEmpty(product.Periods) && !isNilOrEmpty(info.Periods) {
		product.Periods = info.Periods
	}
	if product.SaleStatusByPeriod == nil && info.SaleStatusByPeriod != nil {
		product.SaleStatusByPeriod = info.SaleStatusByPeriod
	}
	if isInvalidStr(product.ProductBusinessType) {
		product.ProductBusinessType = info.ProductBusinessType
	}
	if product.ProductChannelProperties == nil && info.ProductChannelProperties != nil {
		product.ProductChannelProperties = info.ProductChannelProperties
	}
}

func isInvalidStr(s string) bool {
	return s == "" || s == "null"
}

func isNilOrEmpty[T any](arr []T) bool {
	return len(arr) == 0
}

func isNilOrEmptyMap[K comparable, V any](m map[K]V) bool {
	return len(m) == 0
}

// Use b's field value when a field is zero value
func mergeAdditionalAttribute(a, b *models.AdditionalAttribute) {
	if !a.ShelfStatus {
		a.ShelfStatus = b.ShelfStatus
	}
	if a.AttributeName == "" {
		a.AttributeName = b.AttributeName
	}
	if len(a.AttributeNameLan) == 0 {
		a.AttributeNameLan = b.AttributeNameLan
	}
	if a.Code == "" {
		a.Code = b.Code
	}
	if a.Id == "" {
		a.Id = b.Id
	}
	if a.PrintName == "" {
		a.PrintName = b.PrintName
	}
	if len(a.PrintNameLan) == 0 {
		a.PrintNameLan = b.PrintNameLan
	}
	if a.Order == 0 {
		a.Order = b.Order
	}
	if a.SelectedMaxCount == 0 {
		a.SelectedMaxCount = b.SelectedMaxCount
	}
	if !a.SelectedStatus {
		a.SelectedStatus = b.SelectedStatus
	}
	if !a.SingleOrMult {
		a.SingleOrMult = b.SingleOrMult
	}
	if a.PrintOrder == 0 {
		a.PrintOrder = b.PrintOrder
	}
	if len(a.AttributeValues) == 0 {
		a.AttributeValues = b.AttributeValues
	}
	if len(a.AttributeCondition) == 0 {
		a.AttributeCondition = b.AttributeCondition
	}
	if len(a.AttributeLabels) == 0 {
		a.AttributeLabels = b.AttributeLabels
	}
	if a.Remark == "" {
		a.Remark = b.Remark
	}
	if a.ColorConfigId == "" {
		a.ColorConfigId = b.ColorConfigId
	}
	if a.PrintGroupValue == "" {
		a.PrintGroupValue = b.PrintGroupValue
	}
	if !a.FreeSetup {
		a.FreeSetup = b.FreeSetup
	}
	if a.ItemGroupId == "" {
		a.ItemGroupId = b.ItemGroupId
	}
}

// Use b's field value when a field is zero value
func mergeAttributeValues(a, b *models.AttributeValues) {
	if a.Code == "" {
		a.Code = b.Code
	}
	if a.ExtCode == "" {
		a.ExtCode = b.ExtCode
	}
	if a.Id == "" {
		a.Id = b.Id
	}
	if a.Name == "" {
		a.Name = b.Name
	}
	if len(a.NameLan) == 0 {
		a.NameLan = b.NameLan
	}
	if a.Price == 0 {
		a.Price = b.Price
	}
	if a.PrintName == "" {
		a.PrintName = b.PrintName
	}
	if len(a.PrintNameLan) == 0 {
		a.PrintNameLan = b.PrintNameLan
	}
	if a.Order == 0 {
		a.Order = b.Order
	}
	if a.PrintOrder == 0 {
		a.PrintOrder = b.PrintOrder
	}
	if a.SelfHelpOrder == 0 {
		a.SelfHelpOrder = b.SelfHelpOrder
	}
	if a.ShowName == "" {
		a.ShowName = b.ShowName
	}
	if len(a.ShowNameLan) == 0 {
		a.ShowNameLan = b.ShowNameLan
	}
	if a.ColorConfigId == "" {
		a.ColorConfigId = b.ColorConfigId
	}
	if len(a.DisplayMode) == 0 {
		a.DisplayMode = b.DisplayMode
	}
	if a.DifferentChannelAvailablePeriod == nil && b.DifferentChannelAvailablePeriod != nil {
		a.DifferentChannelAvailablePeriod = b.DifferentChannelAvailablePeriod
	}
	if len(a.ExemptsSelectionAttr) == 0 {
		a.ExemptsSelectionAttr = b.ExemptsSelectionAttr
	}
	if a.TakeoutOrder == 0 {
		a.TakeoutOrder = b.TakeoutOrder
	}
	if a.Stock == nil && b.Stock != nil {
		a.Stock = b.Stock
	}
	if len(a.MutexAttrIds) == 0 {
		a.MutexAttrIds = b.MutexAttrIds
	}
	if len(a.MutexSkuAttrIds) == 0 {
		a.MutexSkuAttrIds = b.MutexSkuAttrIds
	}
	if len(a.MutexFeedAttrIds) == 0 {
		a.MutexFeedAttrIds = b.MutexFeedAttrIds
	}
	if len(a.SecondLevelAttr) == 0 {
		a.SecondLevelAttr = b.SecondLevelAttr
	}
	if a.EffectChoose == nil && b.EffectChoose != nil {
		a.EffectChoose = b.EffectChoose
	}
	if len(a.ChannelItemPriceMap) == 0 {
		a.ChannelItemPriceMap = b.ChannelItemPriceMap
	}
	// Boolean fields: no need to check since false is not "empty"
	if !a.DefaultSelectionStatus {
		a.DefaultSelectionStatus = b.DefaultSelectionStatus
	}
	if !a.EnableStatus {
		a.EnableStatus = b.EnableStatus
	}
	if !a.ReadyStatus {
		a.ReadyStatus = b.ReadyStatus
	}
	if !a.SaleStatus {
		a.SaleStatus = b.SaleStatus
	}
	if !a.StockOperate {
		a.StockOperate = b.StockOperate
	}
	if !a.ShelfStatus {
		a.ShelfStatus = b.ShelfStatus
	}
	if !a.CalculateInParent {
		a.CalculateInParent = b.CalculateInParent
	}
}