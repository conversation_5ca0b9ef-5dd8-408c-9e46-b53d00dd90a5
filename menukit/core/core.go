// Package core provides core functionality for the menukit package.
package core

import (
	"context"
	"hexcloud.cn/hicloud/menukit"
	"hexcloud.cn/hicloud/menukit/models"
)

// PullBaseData pulls menu base information from API
func PullBaseData(ctx context.Context, host string, token string, params *menukit.GetMenuParams, options ...menukit.PullDataOption) (*menukit.ProductListOpt, error) {
	return menukit.PullBaseData(ctx, host, token, params, options...)
}

// GetMenuDataLegacy gets menu data from file or cloud (legacy function for backward compatibility)
func GetMenuDataLegacy(ctx context.Context, host string, token string, params *menukit.GetMenuParams, options ...menukit.PullDataOption) (*models.ProductListResponse, error) {
	return menukit.GetMenuDataLegacy(ctx, host, token, params, options...)
}

// GetPriceInfoFromFile gets price information from file
func GetPriceInfoFromFile(queryTime int64, getFunc func(string) ([]byte, error), saveFunc func([]byte, string) error) (*models.PriceInfo, error) {
	return menukit.GetPriceInfoFromFile(queryTime, getFunc, saveFunc)
}