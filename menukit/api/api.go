// Package api provides the public API interfaces for the menukit package.
package api

import (
	"context"
	"hexcloud.cn/hicloud/menukit"
	"hexcloud.cn/hicloud/menukit/models"
)

// GetMenuData retrieves menu data with the specified options
func GetMenuData(ctx context.Context, options ...menukit.Option) (*models.ProductListResponse, error) {
	return menukit.GetMenuData(ctx, options...)
}

// FetchToTemp fetches data to temporary directory only
func FetchToTemp(ctx context.Context, options ...menukit.Option) error {
	return menukit.FetchToTemp(ctx, options...)
}

// MergeTempToProd merges temporary data to production directory
func MergeTempToProd(ctx context.Context, options ...menukit.Option) error {
	return menukit.MergeTempToProd(ctx, options...)
}

// HasTempUpdate checks if there are updates in the temporary directory
func HasTempUpdate(options ...menukit.Option) (bool, error) {
	return menukit.HasTempUpdate(options...)
}