# MenuKit

MenuKit is a Go library for retrieving and processing multi-channel product menu data. It supports fetching data from cloud APIs and provides local caching mechanisms for improved performance and reliability.

## Features

- **Multi-channel support**: Provides customized product menu data for different sales channels
- **Cloud data synchronization**: Able to fetch the latest product and price information from cloud APIs
- **Local caching**: Provides local caching mechanism to use cached data when network is unavailable
- **Incremental updates**: Supports incremental price updates to reduce network transmission
- **Concurrent processing**: Uses goroutines for concurrent data loading to improve performance
- **Error retry**: Built-in HTTP request retry mechanism to improve system stability
- **Price filtering**: Filters invalid products based on price and channel information
- **Flexible directory management**: Supports separation of production and temporary directories
- **Optional data merging strategies**: Supports automatic merging or manual control of the merging process

## Core Components

### 1. Menu Service (menu.go)
Responsible for fetching basic menu data from cloud APIs, supporting both full and incremental update modes.

### 2. Product List Builder (list.go)
Assembles complete product menu data structures from information obtained from multiple data sources.

### 3. Price Service (price.go)
Handles product price information, including current prices and future effective price changes.

### 4. Price Filter (price_filter.go)
Filters product data based on price information and channel requirements.

### 5. API Interface (menukit.go)
Provides a new option-based API interface for more flexible configuration.

## Design Concepts

### 1. Data Fetching Strategy
```
Cloud Data Fetch <---> Local Cache <---> Data Assembly <---> Channel Adaptation
```

The system prioritizes fetching the latest data from the cloud, and uses local cached data as a fallback.

### 2. Concurrent Processing
Uses goroutines to concurrently load local data files, improving data loading speed.

### 3. Incremental Update Mechanism
Implements incremental updates through batch IDs and MD5 checksums, only fetching changed data.

### 4. Error Handling and Recovery
- HTTP request retry mechanism
- Local cache as a fallback for data fetching
- Detailed logging for issue troubleshooting

### 5. Flexible Directory Management
Supports separation of production and temporary data directories, allowing to first fetch to a temporary directory and then merge to the production directory as needed.

## Installation

```bash
go get hexcloud.cn/hicloud/menukit
```

## Usage Examples

### New API Interface (Recommended)

```go
import (
    "context"
    "hexcloud.cn/hicloud/menukit"
)

// 1. Auto fetch and merge (default mode)
menuData, err := menukit.GetMenuData(
    context.Background(),
    menukit.WithHost("https://api.example.com"),
    menukit.WithToken("your-auth-token"),
    menukit.WithStoreID(12345),
    menukit.WithChannelCode("POS"),
    menukit.WithProdDir("/data/prod"),
    menukit.WithTempDir("/data/temp"),
)

// 2. Fetch to temporary directory only
err := menukit.FetchToTemp(
    context.Background(),
    menukit.WithHost("https://api.example.com"),
    menukit.WithToken("your-auth-token"),
    menukit.WithStoreID(12345),
    menukit.WithTempDir("/data/temp"),
)

// 3. Check if there are temporary updates
hasUpdate, err := menukit.HasTempUpdate(
    menukit.WithProdDir("/data/prod"),
    menukit.WithTempDir("/data/temp"),
)
if hasUpdate && err == nil {
    // 4. Merge temporary data to production data
    err := menukit.MergeTempToProd(
        context.Background(),
        menukit.WithProdDir("/data/prod"),
        menukit.WithTempDir("/data/temp"),
    )
    
    // 5. Re-fetch merged data
    menuData, err := menukit.GetMenuData(
        context.Background(),
        menukit.WithProdDir("/data/prod"),
        menukit.WithMode(menukit.ModeProdOnly),
    )
}
```

### Command Line Interface

```bash
# Auto fetch and merge
menucli auto-merge --host https://api.example.com --token your-auth-token --store-id 12345 --channel POS

# Fetch to temporary directory only
menucli fetch-only --host https://api.example.com --token your-auth-token --store-id 12345 --temp-dir /data/temp

# Check for updates
menucli has-update --prod-dir /data/prod --temp-dir /data/temp

# Merge temporary to production
menucli merge-temp --prod-dir /data/prod --temp-dir /data/temp

# Use production data only
menucli prod-only --store-id 12345 --channel POS --prod-dir /data/prod
```

## Configuration Options

### Basic Configuration
```go
WithHost("https://api.example.com")      // API host address
WithToken("your-auth-token")             // Authentication token
WithStoreID(12345)                       // Store ID
WithChannelCode("POS")                   // Channel code
WithLang("zh-CN")                        // Language
```

### Directory Configuration
```go
WithProdDir("/data/prod")                // Production data directory
WithTempDir("/data/temp")                // Temporary data directory
```

### Operation Modes
```go
WithMode(ModeAutoMerge)                  // Auto fetch and merge (default)
WithMode(ModeFetchOnly)                  // Fetch to temporary only
WithMode(ModeMergeTemp)                  // Merge temporary to production
WithMode(ModeProdOnly)                   // Use production data only
```

### Retry Configuration
```go
WithMaxRetries(5)                        // Maximum retry attempts
WithRetryDelay(1 * time.Second)          // Retry delay time
WithExpireInterval(24 * time.Hour)       // Data expiration interval
```

## Performance Optimization

1. **HTTP Client Reuse**: Global HTTP client instance for connection reuse
2. **Buffered File I/O**: Uses buffered read/write to improve file operation performance
3. **Memory Caching**: Avoids repeated file read operations
4. **Concurrent Data Loading**: Uses goroutines to concurrently load local data files

## Testing

Run unit tests:
```bash
go test -v
```

Run benchmark tests:
```bash
go test -bench=.
```

## Contributing

Issues and Pull Requests are welcome to improve this project.

## License

[MIT License](LICENSE)