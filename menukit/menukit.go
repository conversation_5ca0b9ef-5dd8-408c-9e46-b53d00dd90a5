// Package menukit provides functionality for retrieving and processing
// multi-channel product menu data. It supports fetching data from cloud APIs
// and offers local caching mechanisms for improved performance and reliability.
package menukit

import (
	"context"
	"fmt"
	"hexcloud.cn/hicloud/menukit/models"
	"hexcloud.cn/hicloud/menukit/utils"
	"net/http"
	"os"
	"time"
)

// PriceQueryType represents the type of price query
type PriceQueryType int

const (
	// PriceQueryTypeCurrent queries current prices, modify info not returned
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify queries modify info only, returns only top-level modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll queries all price information
	PriceQueryTypeAll PriceQueryType = 3
)

// PriceQueryTypeMap maps string to PriceQueryType
var PriceQueryTypeMap = map[string]PriceQueryType{
	"PriceQueryTypeCurrent": PriceQueryTypeCurrent,
	"PriceQueryTypeModify":  PriceQueryTypeModify,
	"PriceQueryTypeAll":     PriceQueryTypeAll,
}

// GetDataMode represents data retrieval mode
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // Auto fetch and merge
	ModeFetchOnly                    // Fetch to temp only
	ModeMergeTemp                    // Merge temp data to prod
	ModeProdOnly                     // Use prod data only
)

// Default retry configuration
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * 24 * time.Hour
)

// GetMenuParams represents parameters for getting menu data (kept for backward compatibility)
type GetMenuParams struct {
	StoreID     uint64          // storeID: Store ID
	Lang        string          // lang: Language
	ChannelCode string          // channelCode: Channel code
	QueryType   PriceQueryType  // queryType: Price query type
	BatchID     uint64          // batchID: Batch ID
	LocalData   *ProductListOpt `json:"local_data"`
	PartnerID   string          // partnerID: Partner ID
}

// BaseDataRequest represents request for base data
type BaseDataRequest struct {
	BatchID                 string `json:"batchId"`
	Channel                 string `json:"channel"`
	StoreID                 string `json:"storeId"`
	QueryType               string `json:"queryType"` // Query type: 0:current price 2:modify 3:all
	OriginProductListMd5    string `json:"originProductListMd5"`
	OriginProductSpuInfoMd5 string `json:"originProductSpuInfoMd5"`
	OriginAttrListMd5       string `json:"originAttrListMd5"`
	OriginPriceTypeMd5      string `json:"originPriceTypeMd5"`
}

// Options represents configuration options for menukit
type Options struct {
	// Basic configuration
	Host        string // API host address
	Token       string // Authentication token
	StoreID     uint64 // Store ID
	ChannelCode string // Channel code
	Lang        string // Language
	PartnerID   string // Partner ID

	// Directory configuration
	ProdDir string // Production data directory
	TempDir string // Temporary data directory

	// Operation mode
	Mode GetDataMode // Data retrieval mode

	// Retry configuration
	MaxRetries int           // Maximum retry count
	RetryDelay time.Duration // Retry delay time

	// Other configuration
	ExpireInterval   time.Duration // Data expiration time
	NotFilterByPrice bool          // Whether to filter by price
}

// Option represents a configuration option function
type Option func(*Options)

// PullDataOption represents options for pulling data
type PullDataOption func(*pullDataOption)

type pullDataOption struct {
	getFunc          func(string) ([]byte, error)
	saveFunc         func([]byte, string) error
	expireInterval   time.Duration
	maxRetries       int
	retryDelay       time.Duration
	httpClient       *http.Client
	returnBaseData   bool
	notFilterByPrice bool
}

// Basic configuration options
func WithHost(host string) Option {
	return func(opts *Options) {
		opts.Host = host
	}
}

func WithToken(token string) Option {
	return func(opts *Options) {
		opts.Token = token
	}
}

func WithStoreID(storeID uint64) Option {
	return func(opts *Options) {
		opts.StoreID = storeID
	}
}

func WithChannelCode(channelCode string) Option {
	return func(opts *Options) {
		opts.ChannelCode = channelCode
	}
}

func WithLang(lang string) Option {
	return func(opts *Options) {
		opts.Lang = lang
	}
}

func WithPartnerID(partnerID string) Option {
	return func(opts *Options) {
		opts.PartnerID = partnerID
	}
}

// Directory configuration options
func WithProdDir(dir string) Option {
	return func(opts *Options) {
		opts.ProdDir = dir
	}
}

func WithTempDir(dir string) Option {
	return func(opts *Options) {
		opts.TempDir = dir
	}
}

// Mode configuration options
func WithMode(mode GetDataMode) Option {
	return func(opts *Options) {
		opts.Mode = mode
	}
}

// Retry configuration options
func WithMaxRetries(maxRetries int) Option {
	return func(opts *Options) {
		opts.MaxRetries = maxRetries
	}
}

func WithRetryDelay(retryDelay time.Duration) Option {
	return func(opts *Options) {
		opts.RetryDelay = retryDelay
	}
}

func WithExpireInterval(expireInterval time.Duration) Option {
	return func(opts *Options) {
		opts.ExpireInterval = expireInterval
	}
}

func WithNotFilterByPrice(notFilterByPrice bool) Option {
	return func(opts *Options) {
		opts.NotFilterByPrice = notFilterByPrice
	}
}

// PullDataOption functions
func WithGetFunc(getFunc func(string) ([]byte, error)) PullDataOption {
	return func(o *pullDataOption) {
		o.getFunc = getFunc
	}
}

func WithSaveFunc(saveFunc func([]byte, string) error) PullDataOption {
	return func(o *pullDataOption) {
		o.saveFunc = saveFunc
	}
}

func WithHTTPClient(httpClient *http.Client) PullDataOption {
	return func(o *pullDataOption) {
		o.httpClient = httpClient
	}
}

func WithReturnBaseData(returnBaseData bool) PullDataOption {
	return func(o *pullDataOption) {
		o.returnBaseData = returnBaseData
	}
}

// GetMenuData retrieves menu data with the specified options
func GetMenuData(ctx context.Context, options ...Option) (*models.ProductListResponse, error) {
	// Default options
	opts := &Options{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeAutoMerge,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// Execute based on mode
	switch opts.Mode {
	case ModeAutoMerge:
		return getMenuDataAutoMerge(ctx, opts)
	case ModeFetchOnly:
		return nil, FetchToTemp(ctx, options...)
	case ModeMergeTemp:
		return nil, MergeTempToProd(ctx, options...)
	case ModeProdOnly:
		return GetMenuDataFromProd(ctx, options...)
	default:
		return getMenuDataAutoMerge(ctx, opts)
	}
}

// GetMenuDataFromProd retrieves menu data from production directory only
func GetMenuDataFromProd(ctx context.Context, options ...Option) (*models.ProductListResponse, error) {
	// Default options
	opts := &Options{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeProdOnly,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// Create parameters for loading local data
	params := &GetMenuParams{
		StoreID:     opts.StoreID,
		ChannelCode: opts.ChannelCode,
		Lang:        opts.Lang,
		PartnerID:   opts.PartnerID,
		LocalData:   &ProductListOpt{},
	}

	// Load data from production directory only
	prodOpts := applyPullPriceOptions(
		WithGetFunc(DefaultGetFunc(opts.ProdDir)),
		WithSaveFunc(DefaultSaveFunc(opts.ProdDir)),
		WithExpireInterval(opts.ExpireInterval),
		WithMaxRetries(opts.MaxRetries),
		WithRetryDelay(opts.RetryDelay),
	)

	menuOpts, err := loadLocalMenuData(ctx, prodOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to load local menu data from production directory: %w", err)
	}

	// Generate channel product list
	result, err := getChannelProductList(ctx, params.ChannelCode, menuOpts)
	if err != nil {
		return nil, fmt.Errorf("failed to generate channel product list: %w", err)
	}

	return result, nil
}

// FetchToTemp fetches data to temporary directory only
func FetchToTemp(ctx context.Context, options ...Option) error {
	// Default options
	opts := &Options{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeFetchOnly,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// Create parameters for fetching
	params := &GetMenuParams{
		StoreID:     opts.StoreID,
		ChannelCode: opts.ChannelCode,
		Lang:        opts.Lang,
		PartnerID:   opts.PartnerID,
		LocalData:   &ProductListOpt{},
	}

	// Load local data from production directory first (for MD5 comparison)
	prodOpts := applyPullPriceOptions(
		WithGetFunc(DefaultGetFunc(opts.ProdDir)),
		WithSaveFunc(DefaultSaveFunc(opts.ProdDir)),
		WithExpireInterval(opts.ExpireInterval),
		WithMaxRetries(opts.MaxRetries),
		WithRetryDelay(opts.RetryDelay),
	)

	params.LocalData, _ = loadLocalMenuData(ctx, prodOpts)

	// Fetch data to temporary directory
	_, err := PullBaseData(
		ctx,
		opts.Host,
		opts.Token,
		params,
		WithGetFunc(DefaultGetFunc(opts.TempDir)),
		WithSaveFunc(DefaultSaveFunc(opts.TempDir)),
		WithExpireInterval(opts.ExpireInterval),
		WithMaxRetries(opts.MaxRetries),
		WithRetryDelay(opts.RetryDelay),
	)

	return err
}

// MergeTempToProd merges temporary data to production directory
func MergeTempToProd(ctx context.Context, options ...Option) error {
	// Default options
	opts := &Options{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeMergeTemp,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// List of files to copy
	fileNames := []string{
		DefaultPriceFileName,
		DefaultProductListFileName,
		DefaultProductInfoFileName,
		DefaultProductAttrFileName,
		DefaultPriceTypeFileName,
	}

	// Copy files from temp to prod directory
	for _, fileName := range fileNames {
		// Read from temp directory
		tempData, err := utils.DefaultGetFunc(opts.TempDir)(fileName)
		if err != nil {
			logger.Warnf("Failed to read temp file %s: %v", fileName, err)
			continue
		}

		if len(tempData) == 0 {
			logger.Infof("Temp file %s is empty, skipping", fileName)
			continue
		}

		// Save to prod directory
		err = utils.DefaultSaveFunc(opts.ProdDir)(tempData, fileName)
		if err != nil {
			return fmt.Errorf("failed to copy %s from temp to prod: %w", fileName, err)
		}

		logger.Infof("Successfully copied %s from temp to prod directory", fileName)
	}

	return nil
}

// HasTempUpdate checks if there are updates in the temporary directory
func HasTempUpdate(options ...Option) (bool, error) {
	// Default options
	opts := &Options{
		ProdDir: "./prod",
		TempDir: "./temp",
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// List of files to check
	fileNames := []string{
		DefaultPriceFileName,
		DefaultProductListFileName,
		DefaultProductInfoFileName,
		DefaultProductAttrFileName,
		DefaultPriceTypeFileName,
	}

	// Check if any temp file is newer than corresponding prod file
	for _, fileName := range fileNames {
		tempPath := utils.GetFilePath(opts.TempDir, fileName)
		prodPath := utils.GetFilePath(opts.ProdDir, fileName)

		tempInfo, tempErr := os.Stat(tempPath)
		prodInfo, prodErr := os.Stat(prodPath)

		// If temp file exists but prod file doesn't, there's an update
		if tempErr == nil && prodErr != nil {
			return true, nil
		}

		// If both files exist, compare modification times
		if tempErr == nil && prodErr == nil {
			if tempInfo.ModTime().After(prodInfo.ModTime()) {
				return true, nil
			}
		}
	}

	return false, nil
}

// DefaultGetFunc returns a function that reads data from a file in the specified directory
func DefaultGetFunc(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		return utils.DefaultGetFunc(dir)(fileName)
	}
}

// DefaultSaveFunc returns a function that saves data to a file in the specified directory
func DefaultSaveFunc(dir string) func([]byte, string) error {
	return func(data []byte, fileName string) error {
		return utils.DefaultSaveFunc(dir)(data, fileName)
	}
}

// applyPullPriceOptions applies pull price option configuration
func applyPullPriceOptions(options ...PullDataOption) *pullDataOption {
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./"),
		saveFunc:       DefaultSaveFunc("./"),
		httpClient:     GlobalHTTPClient,
	}
	for _, option := range options {
		option(opts)
	}
	return opts
}

// Internal implementation functions
func getMenuDataAutoMerge(ctx context.Context, opts *Options) (*models.ProductListResponse, error) {
	// Implementation for auto-fetch and merge logic
	// This will call the existing logic and add directory separation support
	params := &GetMenuParams{
		StoreID:     opts.StoreID,
		ChannelCode: opts.ChannelCode,
		Lang:        opts.Lang,
		PartnerID:   opts.PartnerID,
		LocalData:   &ProductListOpt{},
	}

	// Use the existing GetMenuDataLegacy function for the actual implementation
	return GetMenuDataLegacy(
		ctx,
		opts.Host,
		opts.Token,
		params,
		WithGetFunc(DefaultGetFunc(opts.ProdDir)),
		WithSaveFunc(DefaultSaveFunc(opts.ProdDir)),
		WithExpireInterval(opts.ExpireInterval),
		WithMaxRetries(opts.MaxRetries),
		WithRetryDelay(opts.RetryDelay),
	)
}
