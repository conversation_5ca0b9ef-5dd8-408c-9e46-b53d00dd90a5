// Package menukit provides functionality for retrieving and processing
// multi-channel product menu data. It supports fetching data from cloud APIs
// and offers local caching mechanisms for improved performance and reliability.
package menukit

import (
	"context"
	"time"
	"hexcloud.cn/hicloud/menukit/models"
)

// GetMenuParams represents parameters for getting menu data (kept for backward compatibility)
type GetMenuParams struct {
	StoreID     uint64         // storeID: Store ID
	Lang        string         // lang: Language
	ChannelCode string         // channelCode: Channel code
	QueryType   PriceQueryType // queryType: Price query type
	BatchID     uint64         // batchID: Batch ID
	LocalData   *ProductListOpt `json:"local_data"`
}

// GetMenuData retrieves menu data with the specified options
func GetMenuData(ctx context.Context, options ...Option) (*models.ProductListResponse, error) {
	// Default options
	opts := &Options{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeAutoMerge,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// Apply options
	for _, option := range options {
		option(opts)
	}

	// Execute based on mode
	switch opts.Mode {
	case ModeAutoMerge:
		return getMenuDataAutoMerge(ctx, opts)
	case ModeFetchOnly:
		return nil, FetchToTemp(ctx, options...)
	case ModeMergeTemp:
		return nil, MergeTempToProd(ctx, options...)
	case ModeProdOnly:
		return GetMenuDataFromProd(ctx, options...)
	default:
		return getMenuDataAutoMerge(ctx, opts)
	}
}

// GetMenuDataFromProd retrieves menu data from production directory only
func GetMenuDataFromProd(ctx context.Context, options ...Option) (*models.ProductListResponse, error) {
	// Implementation for fetching data from production directory only
	// This would load data from the production directory without making network requests
	return nil, nil // Placeholder implementation - would need actual implementation
}

// FetchToTemp fetches data to temporary directory only
func FetchToTemp(ctx context.Context, options ...Option) error {
	// Implementation for fetching to temporary directory only
	return nil // Placeholder implementation - would need actual implementation
}

// MergeTempToProd merges temporary data to production directory
func MergeTempToProd(ctx context.Context, options ...Option) error {
	// Implementation for merging temporary data to production
	// This would copy files from temp directory to prod directory
	return nil // Placeholder implementation - would need actual implementation
}

// HasTempUpdate checks if there are updates in the temporary directory
func HasTempUpdate(options ...Option) (bool, error) {
	// Implementation for checking if there are updates in temporary directory
	// This would compare file modification times or content hashes
	return false, nil // Placeholder implementation - would need actual implementation
}

// Internal implementation functions
func getMenuDataAutoMerge(ctx context.Context, opts *Options) (*models.ProductListResponse, error) {
	// Implementation for auto-fetch and merge logic
	// This will call the existing logic and add directory separation support
	params := &GetMenuParams{
		StoreID:     opts.StoreID,
		ChannelCode: opts.ChannelCode,
		Lang:        opts.Lang,
		LocalData:   &ProductListOpt{},
	}
	
	// Use the existing GetMenuDataLegacy function for the actual implementation
	return GetMenuDataLegacy(
		ctx, 
		opts.Host, 
		opts.Token, 
		params,
		WithGetFunc(DefaultGetFunc(opts.ProdDir)),
		WithSaveFunc(DefaultSaveFunc(opts.ProdDir)),
		WithExpireInterval(opts.ExpireInterval),
		WithMaxRetries(opts.MaxRetries),
		WithRetryDelay(opts.RetryDelay),
	)
}