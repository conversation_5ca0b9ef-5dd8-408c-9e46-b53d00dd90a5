package menukit

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/goccy/go-json"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/buger/jsonparser"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"hexcloud.cn/hicloud/menukit/models"
)

// GetDataMode represents the data retrieval mode
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // Automatically fetch and merge data
	ModeFetchOnly                    // Only fetch to temporary storage
	ModeMergeTemp                    // Merge temporary data to production
	ModeProdOnly                     // Only use production data
)

// Options configures the menu data retrieval
type Options struct {
	// Basic configuration
	Host        string // API host address
	Token       string // Authentication token
	StoreID     uint64 // Store ID
	ChannelCode string // Channel code
	Lang        string // Language

	// Directory configuration
	ProdDir string // Production data directory
	TempDir string // Temporary data directory

	// Operation mode
	Mode      GetDataMode // Data retrieval mode
	AutoMerge bool        // Whether to auto-merge

	// Retry configuration
	MaxRetries int           // Maximum retry attempts
	RetryDelay time.Duration // Retry delay time

	// Other configuration
	ExpireInterval time.Duration // Data expiration interval
}

// Option configures Options
type Option func(*Options)

// WithHost sets the API host address
func WithHost(host string) Option {
	return func(opts *Options) {
		opts.Host = host
	}
}

// WithToken sets the authentication token
func WithToken(token string) Option {
	return func(opts *Options) {
		opts.Token = token
	}
}

// WithStoreID sets the store ID
func WithStoreID(storeID uint64) Option {
	return func(opts *Options) {
		opts.StoreID = storeID
	}
}

// WithChannelCode sets the channel code
func WithChannelCode(channelCode string) Option {
	return func(opts *Options) {
		opts.ChannelCode = channelCode
	}
}

// WithLang sets the language
func WithLang(lang string) Option {
	return func(opts *Options) {
		opts.Lang = lang
	}
}

// WithProdDir sets the production data directory
func WithProdDir(dir string) Option {
	return func(opts *Options) {
		opts.ProdDir = dir
	}
}

// WithTempDir sets the temporary data directory
func WithTempDir(dir string) Option {
	return func(opts *Options) {
		opts.TempDir = dir
	}
}

// WithMode sets the data retrieval mode
func WithMode(mode GetDataMode) Option {
	return func(opts *Options) {
		opts.Mode = mode
	}
}

// WithAutoMerge sets whether to auto-merge
func WithAutoMerge(autoMerge bool) Option {
	return func(opts *Options) {
		opts.AutoMerge = autoMerge
	}
}

// WithExpireIntervalOption sets the data expiration interval
func WithExpireIntervalOption(expireInterval time.Duration) Option {
	return func(opts *Options) {
		opts.ExpireInterval = expireInterval
	}
}

// WithMaxRetriesOption sets the maximum retry attempts
func WithMaxRetriesOption(maxRetries int) Option {
	return func(opts *Options) {
		opts.MaxRetries = maxRetries
	}
}

// WithRetryDelayOption sets the retry delay time
func WithRetryDelayOption(retryDelay time.Duration) Option {
	return func(opts *Options) {
		opts.RetryDelay = retryDelay
	}
}

// PriceQueryType represents the price query type
type PriceQueryType int

const (
	// PriceQueryTypeCurrent queries current price, modify info is not returned
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify queries Modify, only returns outermost Modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll queries all
	PriceQueryTypeAll PriceQueryType = 3
)

var PriceQueryTypeMap = map[string]PriceQueryType{
	"PriceQueryTypeCurrent": PriceQueryTypeCurrent,
	"PriceQueryTypeModify":  PriceQueryTypeModify,
	"PriceQueryTypeAll":     PriceQueryTypeAll,
}

// BaseDataRequest represents the price query request
type BaseDataRequest struct {
	BatchID string `json:"batchId"`
	Channel string `json:"channel"`
	StoreID string `json:"storeId"`
	// Query type 0:current price 2:modify 3:all
	QueryType               string `json:"queryType"`
	OriginProductListMd5    string `json:"originProductListMd5"`
	OriginProductSpuInfoMd5 string `json:"originProductSpuInfoMd5"`
	OriginAttrListMd5       string `json:"originAttrListMd5"`
	OriginPriceTypeMd5      string `json:"originPriceTypeMd5"`
}

// Page represents pagination information
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// Default retry configuration
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)

type pullDataOption struct {
	getFunc        func(string) ([]byte, error)
	saveFunc       func([]byte, string) error
	expireInterval time.Duration
	maxRetries     int
	retryDelay     time.Duration
}

type PullDataOption func(*pullDataOption)

func WithGetFunc(getFunc func(string) ([]byte, error)) PullDataOption {
	return func(o *pullDataOption) {
		o.getFunc = getFunc
	}
}

func WithSaveFunc(saveFunc func([]byte, string) error) PullDataOption {
	return func(o *pullDataOption) {
		o.saveFunc = saveFunc
	}
}

func WithExpireInterval(expireInterval time.Duration) PullDataOption {
	return func(o *pullDataOption) {
		o.expireInterval = expireInterval
	}
}

func WithMaxRetries(maxRetries int) PullDataOption {
	return func(o *pullDataOption) {
		o.maxRetries = maxRetries
	}
}

func WithRetryDelay(retryDelay time.Duration) PullDataOption {
	return func(o *pullDataOption) {
		o.retryDelay = retryDelay
	}
}

// sendBaseDataRequest sends a price query request and handles retry logic
// url: request URL
// req: request body
// token: authentication token
// maxRetries: maximum retry attempts
// retryDelay: retry interval time
func sendBaseDataRequest(ctx context.Context, host string, token string, params *GetMenuParams, opts *pullDataOption) ([]byte, error) {
	// Serialize request body
	req := &BaseDataRequest{
		BatchID:                 cast.ToString(params.BatchID),
		Channel:                 params.ChannelCode,
		StoreID:                 cast.ToString(params.StoreID),
		QueryType:               cast.ToString(int(params.QueryType)),
		OriginProductListMd5:    *params.LocalData.OriginProductListMd5,
		OriginProductSpuInfoMd5: *params.LocalData.OriginProductSpuInfoMd5,
		OriginAttrListMd5:       *params.LocalData.OriginAttrListMd5,
		OriginPriceTypeMd5:      *params.LocalData.OriginPriceTypeMd5,
	}
	url := fmt.Sprintf("%s%s", host, DefaultBaseDataAPi)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize request: %w", err)
	}

	// Retry logic
	var respBody []byte
	var lastErr error

	for i := 0; i <= opts.maxRetries; i++ {
		// If not the first attempt, wait before retrying
		if i > 0 {
			time.Sleep(opts.retryDelay)
		}
		// Create HTTP request
		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, fmt.Errorf("failed to create HTTP request: %w", err)
		}

		// Set request headers
		httpReq.Header.Set("Content-Type", "application/json")
		if token != "" {
			httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
		}
		// Set language
		if params.Lang != "" {
			httpReq.Header.Set("Accept-Language", params.Lang)
		}
		respBody, err = getHttpResponse(httpReq, GlobalHTTPClient)
		if err != nil {
			lastErr = err
			continue
		}
		return respBody, nil
	}

	// All retries failed, return the last error
	return nil, lastErr
}

// PullBaseData pulls menu base information from API
// host: API host address
// token: authentication token
// storeID: store ID
// saveFunc: function to save price information
// getFunc: function to get existing price information
func PullBaseData(ctx context.Context, host string, token string, params *GetMenuParams, options ...PullDataOption) (*ProductListOpt, error) {
	currentPriceInfo := &models.PriceInfo{}

	// Apply option configuration
	opts := applyPullPriceOptions(options...)

	// Read existing price information
	bs, err := opts.getFunc(DefaultPriceFileName)
	if err != nil {
		return nil, errors.Join(errors.New("failed to read existing price information: "), err)
	}

	if len(bs) > 0 {
		if err := json.Unmarshal(bs, currentPriceInfo); err != nil {
			return nil, errors.Join(errors.New("failed to deserialize existing price information: "), err)
		}
	}

	// Get batch ID and query type
	params.BatchID, params.QueryType, err = getBatchIdAndQueryType(currentPriceInfo, opts.expireInterval)
	if err != nil {
		logger.Errorf("Failed to get batch ID and query type: %v, changed to full fetch (may be first fetch)", err)
	}

	start := time.Now()
	respBody, err := sendBaseDataRequest(ctx, host, token, params, opts)
	if err != nil {
		return nil, err
	}
	logger.Infof("Fetch base cloud data: baseData, size: %s, time: %v", formatDataSize(len(respBody)), time.Since(start))

	priceInfo, err := buildPriceInfoByResponse(respBody, currentPriceInfo, params.QueryType)
	if err != nil {
		return nil, err
	}
	priceInfoBs, err := json.Marshal(priceInfo)
	if err != nil {
		return nil, err
	}
	if err := opts.saveFunc(priceInfoBs, DefaultPriceFileName); err != nil {
		return nil, fmt.Errorf("failed to save price information: %w", err)
	}
	params.LocalData.PriceInfo = priceInfo
	err = buildAndSaveBaseData(ctx, params.LocalData, respBody, opts.saveFunc)
	if err != nil {
		return nil, err
	}
	return params.LocalData, nil
}

// GetMenuDataLegacy gets menu data from file or cloud (legacy function for backward compatibility)
func GetMenuDataLegacy(ctx context.Context, host string, token string, params *GetMenuParams, options ...PullDataOption) (*models.ProductListResponse, error) {
	// Apply option configuration
	opts := applyPullPriceOptions(options...)

	var menuOpts *ProductListOpt
	var err error

	logger.Infof("Start getting menu data - Store ID: %d, Channel: %s", params.StoreID, params.ChannelCode)

	// Try to fetch data from cloud first
	start := time.Now()
	//{
	//    "batchId": "5020927675187920896",
	//    "channel": "POS",
	//    "storeId": "4972806615293067264",
	//    "queryType": "2",
	//    "originProductListMd5":"89de39d677222fbb2f278053146023d4",
	//    "originProductSpuInfoMd5":"185e2bf66567f6bc9efc651436da0406",
	//    "originAttrListMd5":"4aa46649699733c0edf6386cc5e6523b",
	//    "originPriceTypeMd5":"a291c2d9a884075147337328a12fddb6"
	//}
	params.LocalData, err = loadLocalMenuData(ctx, opts)
	logger.Infof("loadLocalMenuData time: %v", time.Since(start))
	if err != nil {
		logger.Errorf("Local cache read failed: local error=%v", err)
	}
	menuOpts, err = PullBaseData(ctx, host, token, params, options...)
	elapsed := time.Since(start)
	logger.Infof("Cloud data fetch time: %s", elapsed.String())
	if err != nil {
		logger.Warnf("Failed to fetch cloud data: %v, trying to use local cache data", err)
		// Use err group to read local data concurrently
		menuOpts = params.LocalData
		logger.Info("Successfully used local cache data")
	} else {
		logger.Info("Successfully fetched data from cloud")
	}

	// Generate channel product list
	start = time.Now()
	result, err := getChannelProductList(ctx, params.ChannelCode, menuOpts)
	logger.Infof("Assembly time: %v", time.Since(start))
	if err != nil {
		return nil, fmt.Errorf("failed to generate channel product list: %w", err)
	}
	return result, nil
}

// applyPullPriceOptions applies pull price option configuration
func applyPullPriceOptions(options ...PullDataOption) *pullDataOption {
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./"),
		saveFunc:       DefaultSaveFunc("./"),
	}
	for _, option := range options {
		option(opts)
	}
	return opts
}

// Define file read tasks
type fileTask struct {
	fileName        string
	unmarshalTarget any
	name            string
	md5String       *string
}

// loadLocalMenuData loads local menu data concurrently
func loadLocalMenuData(ctx context.Context, opts *pullDataOption) (*ProductListOpt, error) {
	menuOpts := &ProductListOpt{
		ProductList:             make([]*models.Category, 0),
		ProductInfo:             make([]*models.Product, 0),
		ProductAttr:             make([]*models.AdditionalAttribute, 0),
		PriceInfo:               &models.PriceInfo{},
		PriceTypeInfo:           make([]*models.PriceType, 0),
		OriginPriceTypeMd5:      new(string),
		OriginProductSpuInfoMd5: new(string),
		OriginAttrListMd5:       new(string),
		OriginProductListMd5:    new(string),
	}

	// Use channels to collect results, avoiding concurrent writes to shared memory
	priceTypeChan := make(chan []*models.PriceType, 1)
	productInfoChan := make(chan []*models.Product, 1)
	productAttrChan := make(chan []*models.AdditionalAttribute, 1)
	productListChan := make(chan []*models.Category, 1)

	priceTypeMd5Chan := make(chan string, 1)
	productInfoMd5Chan := make(chan string, 1)
	productAttrMd5Chan := make(chan string, 1)
	productListMd5Chan := make(chan string, 1)

	// Error channel
	errChan := make(chan error, 5) // 5 goroutines, reserve enough buffer

	g, ctx := errgroup.WithContext(ctx)

	// Handle price information separately
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			data, err := GetPriceInfoFromFile(0, opts.getFunc, opts.saveFunc)
			if err != nil {
				errChan <- fmt.Errorf("failed to read %s file: %w", "price", err)
				return err
			}
			menuOpts.PriceInfo = data
			return nil
		}
	})

	// Concurrently read price type file
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultPriceTypeFileName)
			if err != nil {
				errChan <- fmt.Errorf("failed to read %s file: %w", "price_type", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			priceTypeMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("Local file %s is empty, skipping", "price_type")
				priceTypeChan <- make([]*models.PriceType, 0)
				return nil
			}

			var priceTypes []*models.PriceType
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&priceTypes)
			err = json.Unmarshal(data, &priceTypes)
			if err != nil {
				errChan <- fmt.Errorf("failed to parse %s file: %w", "price_type", err)
				return err
			}

			logger.Infof("Successfully read local file: %s, size: %s, time: %v", "price_type", formatDataSize(len(data)), time.Since(start))
			priceTypeChan <- priceTypes
			return nil
		}
	})

	// Concurrently read product info file
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductInfoFileName)
			if err != nil {
				errChan <- fmt.Errorf("failed to read %s file: %w", "product_info", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productInfoMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("Local file %s is empty, skipping", "product_info")
				productInfoChan <- make([]*models.Product, 0)
				return nil
			}

			var products []*models.Product
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&products)
			err = json.Unmarshal(data, &products)
			if err != nil {
				errChan <- fmt.Errorf("failed to parse %s file: %w", "product_info", err)
				return err
			}

			logger.Infof("Successfully read local file: %s, size: %s, time: %v", "product_info", formatDataSize(len(data)), time.Since(start))
			productInfoChan <- products
			return nil
		}
	})

	// Concurrently read product attribute file
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductAttrFileName)
			if err != nil {
				errChan <- fmt.Errorf("failed to read %s file: %w", "product_attr", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productAttrMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("Local file %s is empty, skipping", "product_attr")
				productAttrChan <- make([]*models.AdditionalAttribute, 0)
				return nil
			}

			var attributes []*models.AdditionalAttribute
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&attributes)
			err = json.Unmarshal(data, &attributes)
			if err != nil {
				errChan <- fmt.Errorf("failed to parse %s file: %w", "product_attr", err)
				return err
			}

			logger.Infof("Successfully read local file: %s, size: %s, time: %v", "product_attr", formatDataSize(len(data)), time.Since(start))
			productAttrChan <- attributes
			return nil
		}
	})

	// Concurrently read product list file
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductListFileName)
			if err != nil {
				errChan <- fmt.Errorf("failed to read %s file: %w", "product_list", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productListMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("Local file %s is empty, skipping", "product_list")
				productListChan <- make([]*models.Category, 0)
				return nil
			}

			var categories []*models.Category
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&categories)
			err = json.Unmarshal(data, &categories)
			if err != nil {
				errChan <- fmt.Errorf("failed to parse %s file: %w", "product_list", err)
				return err
			}

			logger.Infof("Successfully read local file: %s, size: %s, time: %v", "product_list", formatDataSize(len(data)), time.Since(start))
			productListChan <- categories
			return nil
		}
	})

	// Wait for all goroutines to complete
	if err := g.Wait(); err != nil {
		close(errChan)
		// Collect all errors
		var errMsgs []string
		for err := range errChan {
			errMsgs = append(errMsgs, err.Error())
		}
		if len(errMsgs) > 0 {
			return menuOpts, fmt.Errorf("concurrent local file read failed: %s", strings.Join(errMsgs, "; "))
		}
		return menuOpts, fmt.Errorf("concurrent local file read failed: %w", err)
	}

	// Close channels
	close(priceTypeChan)
	close(productInfoChan)
	close(productAttrChan)
	close(productListChan)
	close(priceTypeMd5Chan)
	close(productInfoMd5Chan)
	close(productAttrMd5Chan)
	close(productListMd5Chan)
	close(errChan)

	// Read results from channels and assign to menuOpts
	menuOpts.PriceTypeInfo = <-priceTypeChan
	menuOpts.ProductInfo = <-productInfoChan
	menuOpts.ProductAttr = <-productAttrChan
	menuOpts.ProductList = <-productListChan

	*menuOpts.OriginPriceTypeMd5 = <-priceTypeMd5Chan
	*menuOpts.OriginProductSpuInfoMd5 = <-productInfoMd5Chan
	*menuOpts.OriginAttrListMd5 = <-productAttrMd5Chan
	*menuOpts.OriginProductListMd5 = <-productListMd5Chan

	logger.Info("All local files read completed")
	return menuOpts, nil
}

// Get and other information (product_info product_attr price_type product_list) return map[string][]byte filename and content
func buildAndSaveBaseData(ctx context.Context, menuOpts *ProductListOpt, resp []byte, savefunc func([]byte, string) error) error {
	tasks := []fileTask{
		{DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "priceType", menuOpts.OriginPriceTypeMd5},
		{DefaultProductInfoFileName, &menuOpts.ProductInfo, "productSpuInfo", menuOpts.OriginProductSpuInfoMd5},
		{DefaultProductAttrFileName, &menuOpts.ProductAttr, "attrList", menuOpts.OriginAttrListMd5},
		{DefaultProductListFileName, &menuOpts.ProductList, "productList", menuOpts.OriginProductListMd5},
	}
	for _, task := range tasks {
		task := task // Avoid closure issue
		data, _, _, err := jsonparser.Get(resp, "payload", task.name)
		if err != nil {
			return fmt.Errorf("failed to get %s: %w", task.name, err)
		}
		rows, _, _, err := jsonparser.Get(data, "rows")
		if err != nil {
			return fmt.Errorf("failed to get %s.rows: %w", task.name, err)
		}
		if cast.ToString(rows) == "null" {
			logger.Infof("%s: local data and cloud data are consistent, using local data", task.name)
			continue
		}
		//dc := decoder.NewDecoder(string(rows))
		//dc.UseNumber()
		//err = dc.Decode(task.unmarshalTarget)
		err = json.Unmarshal(rows, task.unmarshalTarget)
		logger.Infof("Successfully read cloud data: %s, size: %s", task.name, formatDataSize(len(data)))
		if err := savefunc(data, task.fileName); err != nil {
			return fmt.Errorf("failed to save %s: %w", task.name, err)
		}
	}
	return nil
}

func getHttpResponse(httpReq *http.Request, client *http.Client) ([]byte, error) {
	// Send request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close() // Ensure response body is closed before function exits

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned error status code: %d", resp.StatusCode)
	}

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status_code
	statusCode, err := jsonparser.GetInt(respBody, "status_code")
	if err != nil {
		return nil, fmt.Errorf("failed to read response status code: %w", err)
	}
	if statusCode != 200 {
		return nil, fmt.Errorf("API returned error status code: %d, resp:%s", statusCode, respBody)
	}
	// Request successful, break loop
	return respBody, nil
}
