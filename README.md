# OmniChannel Product List

OmniChannel Product List 是一个用于获取和处理多渠道商品菜单数据的Go语言库。它支持从云端API拉取数据，并提供了本地缓存机制以提高性能和可靠性。

## 功能特性

- **多渠道支持**：支持为不同销售渠道提供定制化的商品菜单数据
- **云端数据同步**：能够从云端API拉取最新的商品和价格信息
- **本地缓存**：提供本地缓存机制，在网络不可用时使用缓存数据
- **增量更新**：支持增量更新价格信息，减少网络传输
- **并发处理**：使用goroutines并发处理数据加载，提高性能
- **错误重试**：内置HTTP请求重试机制，提高系统稳定性
- **价格过滤**：根据价格信息和渠道信息过滤无效商品
- **灵活的目录管理**：支持生产目录和暂存目录分离
- **可选的数据合并策略**：支持自动合并或手动控制合并过程

## 核心组件

### 1. Menu Service (menu.go)
负责从云端API获取基础菜单数据，支持全量和增量更新模式。

### 2. Product List Builder (list.go)
将从多个数据源获取的信息组装成完整的商品菜单数据结构。

### 3. Price Service (price.go)
处理商品价格信息，包括当前价格和未来生效的价格变更。

### 4. Price Filter (price_filter.go)
根据价格信息和渠道要求过滤商品数据。

### 5. API Interface (api.go)
提供新的选项模式API接口，支持更灵活的配置。

## 设计思路

### 1. 数据获取策略
```
云端数据获取 <---> 本地缓存 <---> 数据组装 <---> 渠道适配
```

系统优先尝试从云端获取最新数据，如果失败则使用本地缓存数据。

### 2. 并发处理
使用goroutines并发加载本地数据文件，提高数据加载速度。

### 3. 增量更新机制
通过批次ID和MD5校验实现增量更新，只获取变更的数据。

### 4. 错误处理和恢复
- HTTP请求重试机制
- 本地缓存作为数据获取的后备方案
- 详细的日志记录便于问题排查

### 5. 灵活的目录管理
支持生产数据目录和暂存数据目录分离，可以先拉取到暂存目录，再根据需要合并到生产目录。

## 安装

```bash
go get hexcloud.cn/hicloud/omnichannel-product-list
```

## 使用示例

### 新API接口（推荐）

```go
import (
    "context"
    "hexcloud.cn/hicloud/omnichannel-product-list"
)

// 1. 自动拉取并合并（默认模式）
menuData, err := omnichannel_product_list.GetMenuData(
    context.Background(),
    omnichannel_product_list.WithHost("https://api.example.com"),
    omnichannel_product_list.WithToken("your-auth-token"),
    omnichannel_product_list.WithStoreID(12345),
    omnichannel_product_list.WithChannelCode("POS"),
    omnichannel_product_list.WithProdDir("/data/prod"),
    omnichannel_product_list.WithTempDir("/data/temp"),
)

// 2. 仅拉取到暂存目录
err := omnichannel_product_list.FetchToTemp(
    context.Background(),
    omnichannel_product_list.WithHost("https://api.example.com"),
    omnichannel_product_list.WithToken("your-auth-token"),
    omnichannel_product_list.WithStoreID(12345),
    omnichannel_product_list.WithTempDir("/data/temp"),
)

// 3. 检查是否有暂存更新
hasUpdate, err := omnichannel_product_list.HasTempUpdate(
    omnichannel_product_list.WithProdDir("/data/prod"),
    omnichannel_product_list.WithTempDir("/data/temp"),
)
if hasUpdate && err == nil {
    // 4. 合并暂存数据到生产数据
    err := omnichannel_product_list.MergeTempToProd(
        context.Background(),
        omnichannel_product_list.WithProdDir("/data/prod"),
        omnichannel_product_list.WithTempDir("/data/temp"),
    )
    
    // 5. 重新获取合并后的数据
    menuData, err := omnichannel_product_list.GetMenuData(
        context.Background(),
        omnichannel_product_list.WithProdDir("/data/prod"),
        omnichannel_product_list.WithMode(omnichannel_product_list.ModeProdOnly),
    )
}
```

### 旧API接口（向后兼容）

```go
import (
    "context"
    "hexcloud.cn/hicloud/omnichannel-product-list"
)

// 获取菜单数据（向后兼容的旧接口）
menuData, err := omnichannel_product_list.GetMenuData(
    context.Background(),
    "https://api.example.com",  // API主机地址
    "your-auth-token",          // 认证令牌
    &omnichannel_product_list.GetMenuParams{
        StoreID:     12345,         // 店铺ID
        ChannelCode: "POS",         // 渠道代码
        Lang:        "zh-CN",       // 语言
    },
)
```

## 配置选项

### 基础配置
```go
WithHost("https://api.example.com")      // API主机地址
WithToken("your-auth-token")             // 认证令牌
WithStoreID(12345)                       // 店铺ID
WithChannelCode("POS")                   // 渠道代码
WithLang("zh-CN")                        // 语言
```

### 目录配置
```go
WithProdDir("/data/prod")                // 生产数据目录
WithTempDir("/data/temp")                // 暂存数据目录
```

### 操作模式
```go
WithMode(ModeAutoMerge)                  // 自动拉取并合并（默认）
WithMode(ModeFetchOnly)                  // 仅拉取到暂存
WithMode(ModeMergeTemp)                  // 合并暂存到生产
WithMode(ModeProdOnly)                   // 仅使用生产数据
```

### 重试配置
```go
WithMaxRetries(5)                        // 最大重试次数
WithRetryDelay(1 * time.Second)          // 重试延迟时间
WithExpireInterval(24 * time.Hour)       // 数据过期时间
```

## 性能优化

1. **HTTP客户端复用**：全局HTTP客户端实例，复用连接
2. **带缓冲的文件IO**：使用缓冲读写提高文件操作性能
3. **内存缓存**：避免重复的文件读取操作
4. **并发数据加载**：使用goroutines并发加载本地数据文件

## 测试

运行单元测试：
```bash
go test -v
```

运行基准测试：
```bash
go test -bench=.
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

[MIT License](LICENSE)