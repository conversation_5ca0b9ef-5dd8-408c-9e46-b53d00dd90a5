package omnichannel_product_list

//
//import (
//	"context"
//	_ "embed"
//	"log"
//	"os"
//	"testing"
//	"time"
//)
//
//var productList string
//
//var productInfo string
//
//var productAttr string
//
//var priceInfo string
//
//var priceTypeInfo string
//
//func TestGetChannelProductList(t *testing.T) {
//	type args struct {
//		productList []byte
//		productInfo []byte
//		productAttr []byte
//	}
//	tests := []struct {
//		name    string
//		args    args
//		want    []byte
//		wantErr bool
//	}{
//		{
//			name: "case1",
//			args: args{
//				productList: []byte(productList),
//				productInfo: []byte(productInfo),
//				productAttr: []byte(productAttr),
//			},
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			start := time.Now()
//			t.Log("开始执行")
//			opts := &productListOpt{
//				ProductList:   tt.args.productList,
//				SetList:       tt.args.productInfo,
//				ProductAttr:   tt.args.productAttr,
//				PriceInfo:     []byte(priceInfo),
//				PriceTypeInfo: []byte(priceTypeInfo),
//			}
//			list, err := getChannelProductList(context.Background(), "POS", opts)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("getChannelProductList() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			elapsed := time.Since(start)
//			t.Log("执行时间: ", elapsed.Milliseconds())
//			if err := os.WriteFile("file.json", list, 0666); err != nil {
//				log.Fatal(err)
//			}
//			return
//		})
//	}
//}
//
//// 基准测试
//func BenchmarkGetChannelProductList(b *testing.B) {
//	// 准备测试数据
//	productListBytes := []byte(productList)
//	productInfoBytes := []byte(productInfo)
//	productAttrBytes := []byte(productAttr)
//	priceInfoBytes := []byte(priceInfo)
//	priceTypeInfoBytes := []byte(priceTypeInfo)
//
//	// 重置计时器
//	b.ResetTimer()
//
//	// 运行 b.N 次测试
//	for i := 0; i < b.N; i++ {
//		// 暂停计时器，不计算准备工作的时间
//		b.StopTimer()
//		// 每次测试前复制数据，确保测试不受到上一次测试的影响
//		productListCopy := make([]byte, len(productListBytes))
//		copy(productListCopy, productListBytes)
//		productInfoCopy := make([]byte, len(productInfoBytes))
//		copy(productInfoCopy, productInfoBytes)
//		productAttrCopy := make([]byte, len(productAttrBytes))
//		copy(productAttrCopy, productAttrBytes)
//		priceInfoCopy := make([]byte, len(priceInfoBytes))
//		copy(priceInfoCopy, priceInfoBytes)
//		priceTypeInfoCopy := make([]byte, len(priceTypeInfoBytes))
//		copy(priceTypeInfoCopy, priceTypeInfoBytes)
//		opts := &productListOpt{
//			ProductList:   productListCopy,
//			SetList:       productInfoCopy,
//			ProductAttr:   productAttrCopy,
//			PriceInfo:     priceInfoCopy,
//			PriceTypeInfo: priceTypeInfoCopy,
//		}
//		// 恢复计时器
//		b.StartTimer()
//
//		// 执行测试
//		_, err := getChannelProductList(context.Background(), "POS", opts)
//		if err != nil {
//			b.Fatalf("getChannelProductList() error = %v", err)
//		}
//	}
//}
//
//// 内存分配基准测试
//func BenchmarkGetChannelProductList_Alloc(b *testing.B) {
//	// 准备测试数据
//	productListBytes := []byte(productList)
//	productInfoBytes := []byte(productInfo)
//	productAttrBytes := []byte(productAttr)
//	priceInfoBytes := []byte(priceInfo)
//	priceTypeInfoBytes := []byte(priceTypeInfo)
//	opts := &productListOpt{
//		ProductList:   productListBytes,
//		SetList:       productInfoBytes,
//		ProductAttr:   productAttrBytes,
//		PriceInfo:     priceInfoBytes,
//		PriceTypeInfo: priceTypeInfoBytes,
//	}
//
//	// 重置计时器和内存统计
//	b.ResetTimer()
//	b.ReportAllocs()
//
//	// 运行 b.N 次测试
//	for i := 0; i < b.N; i++ {
//		_, err := getChannelProductList(context.Background(), "POS", opts)
//		if err != nil {
//			b.Fatalf("getChannelProductList() error = %v", err)
//		}
//	}
//}
//
//// 并行基准测试
//func BenchmarkGetChannelProductList_Parallel(b *testing.B) {
//	// 准备测试数据
//	productListBytes := []byte(productList)
//	productInfoBytes := []byte(productInfo)
//	productAttrBytes := []byte(productAttr)
//	priceInfoBytes := []byte(priceInfo)
//	priceTypeInfoBytes := []byte(priceTypeInfo)
//	opts := &productListOpt{
//		ProductList:   productListBytes,
//		SetList:       productInfoBytes,
//		ProductAttr:   productAttrBytes,
//		PriceInfo:     priceInfoBytes,
//		PriceTypeInfo: priceTypeInfoBytes,
//	}
//
//	// 重置计时器
//	b.ResetTimer()
//
//	// 并行执行测试
//	b.RunParallel(func(pb *testing.PB) {
//		for pb.Next() {
//			_, err := getChannelProductList(context.Background(), "POS", opts)
//			if err != nil {
//				b.Fatalf("getChannelProductList() error = %v", err)
//			}
//		}
//	})
//}
