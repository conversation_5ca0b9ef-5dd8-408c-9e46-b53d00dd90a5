package omnichannel_product_list

import (
	"context"
	"testing"
	"time"
)

// TestLoadLocalMenuDataConcurrency 测试loadLocalMenuData函数的并发安全性
func TestLoadLocalMenuDataConcurrency(t *testing.T) {
	// 创建模拟的pullDataOption
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./test_data"), // 使用测试数据目录
		saveFunc:       DefaultSaveFunc("./test_data"),
	}

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 运行loadLocalMenuData函数
	menuOpts, err := loadLocalMenuData(ctx, opts)
	if err != nil {
		// 由于测试数据目录不存在，预期会出现错误，这是正常的
		t.Logf("Expected error occurred: %v", err)
	}

	// 验证返回的结构体不为nil
	if menuOpts == nil {
		t.Error("menuOpts should not be nil")
	}

	// 验证各个字段已正确初始化
	if menuOpts.ProductList == nil {
		t.Error("ProductList should not be nil")
	}
	
	if menuOpts.ProductInfo == nil {
		t.Error("ProductInfo should not be nil")
	}
	
	if menuOpts.ProductAttr == nil {
		t.Error("ProductAttr should not be nil")
	}
	
	if menuOpts.PriceInfo == nil {
		t.Error("PriceInfo should not be nil")
	}
	
	if menuOpts.PriceTypeInfo == nil {
		t.Error("PriceTypeInfo should not be nil")
	}
	
	// 验证MD5指针字段不为nil
	if menuOpts.OriginPriceTypeMd5 == nil {
		t.Error("OriginPriceTypeMd5 should not be nil")
	}
	
	if menuOpts.OriginProductSpuInfoMd5 == nil {
		t.Error("OriginProductSpuInfoMd5 should not be nil")
	}
	
	if menuOpts.OriginAttrListMd5 == nil {
		t.Error("OriginAttrListMd5 should not be nil")
	}
	
	if menuOpts.OriginProductListMd5 == nil {
		t.Error("OriginProductListMd5 should not be nil")
	}
}