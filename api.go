package omnichannel_product_list

import (
	"context"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
	"os"
	"path/filepath"
	"sync"
	"time"
)

var GlobalMutex = sync.RWMutex{}

// GetDataMode 数据获取模式
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // 自动拉取并合并
	ModeFetchOnly                    // 仅拉取到暂存
	ModeMergeTemp                    // 合并暂存数据到生产
	ModeProdOnly                     // 仅使用生产数据
)

type DirType int

const (
	DirTypeTemp DirType = iota
	DirTypeProd
)

// GetMenuDataOptions 获取菜单数据的选项配置
type GetMenuDataOptions struct {
	// 基础配置
	Host        string // API主机地址
	Token       string // 认证令牌
	StoreID     string // 店铺ID
	ChannelCode string // 渠道代码
	Lang        string // 语言

	// 目录配置
	ProdDir string // 生产数据目录
	TempDir string // 暂存数据目录

	// 操作模式
	Mode      GetDataMode // 数据获取模式
	AutoMerge bool        // 是否自动合并

	// 重试配置
	MaxRetries int           // 最大重试次数
	RetryDelay time.Duration // 重试延迟时间

	// 其他配置
	ExpireInterval time.Duration // 数据过期时间
	// 租户ID
	PartnerID string
}

// WithOption 选项配置函数
type WithOption func(*GetMenuDataOptions)

// 基础配置选项
func WithHost(host string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.Host = host
	}
}

func WithToken(token string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.Token = token
	}
}

func WithStoreID(storeID string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.StoreID = storeID
	}
}
func WithPartnerID(partnerID string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.PartnerID = partnerID
	}
}

func WithChannelCode(channelCode string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.ChannelCode = channelCode
	}
}

func WithLang(lang string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.Lang = lang
	}
}

// 目录配置选项
func WithProdDir(dir string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.ProdDir = dir
	}
}

func WithTempDir(dir string) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.TempDir = dir
	}
}

// 操作模式选项
func WithMode(mode GetDataMode) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.Mode = mode
	}
}

func WithAutoMerge(autoMerge bool) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.AutoMerge = autoMerge
	}
}

// WithExpireIntervalOption 重试配置选项
func WithExpireIntervalOption(expireInterval time.Duration) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.ExpireInterval = expireInterval
	}
}

// WithMaxRetriesOption 重试配置选项
func WithMaxRetriesOption(maxRetries int) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.MaxRetries = maxRetries
	}
}

// WithRetryDelayOption 重试配置选项
func WithRetryDelayOption(retryDelay time.Duration) WithOption {
	return func(opts *GetMenuDataOptions) {
		opts.RetryDelay = retryDelay
	}
}

// 新的GetMenuDataV2函数签名
func GetMenuDataV2(ctx context.Context, options ...WithOption) (*model.ProductListResponse, error) {
	// 默认选项配置
	opts := &GetMenuDataOptions{
		Host:           DefaultHost,
		ProdDir:        "./prod",
		TempDir:        "./temp",
		Mode:           ModeAutoMerge,
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	// 应用选项
	for _, option := range options {
		option(opts)
	}

	// 根据模式执行不同逻辑
	switch opts.Mode {
	case ModeAutoMerge:
		return getMenuDataAutoMerge(ctx, opts)
	case ModeFetchOnly:
		return nil, fetchToTemp(ctx, opts)
	case ModeMergeTemp:
		return nil, mergeTempToProd(ctx, opts)
	case ModeProdOnly:
		return getMenuDataFromProd(ctx, opts)
	default:
		return getMenuDataAutoMerge(ctx, opts)
	}
}

// FetchToTempV2 仅拉取数据到暂存目录
func FetchToTempV2(ctx context.Context, options ...WithOption) error {
	opts := &GetMenuDataOptions{
		Host:           DefaultHost,
		TempDir:        "./temp",
		MaxRetries:     3,
		RetryDelay:     500 * time.Millisecond,
		ExpireInterval: 7 * 24 * time.Hour,
	}

	for _, option := range options {
		option(opts)
	}

	return fetchToTemp(ctx, opts)
}

// MergeTempToProdV2 将暂存数据合并到生产数据
func MergeTempToProdV2(ctx context.Context, options ...WithOption) error {
	opts := &GetMenuDataOptions{
		ProdDir: "./prod",
		TempDir: "./temp",
	}

	for _, option := range options {
		option(opts)
	}

	return mergeTempToProd(ctx, opts)
}

// HasTempUpdateV2 检查暂存目录是否有更新
func HasTempUpdateV2(options ...WithOption) (bool, error) {
	opts := &GetMenuDataOptions{
		ProdDir: "./prod",
		TempDir: "./temp",
	}

	for _, option := range options {
		option(opts)
	}

	return hasTempUpdate(opts)
}

// 内部实现函数
func getMenuDataAutoMerge(ctx context.Context, opts *GetMenuDataOptions) (*model.ProductListResponse, error) {
	// 检查暂存目录是否有更新
	GlobalMutex.Lock()
	defer GlobalMutex.Unlock()
	hasUpdate, err := hasTempUpdate(opts)
	if err != nil {
		return nil, err
	}
	if hasUpdate {
		// 全量覆盖的文件直接通过覆盖 避免重复marshal
		err = overrideFile(ctx, opts)
		if err != nil {
			return nil, err
		}
	}
	getFunc := DefaultGetFuncWithBuffer(opts.ProdDir)
	saveFunc := DefaultSaveFuncWithBuffer(opts.ProdDir)
	return GetMenuData(ctx, opts.Host, opts.Token, &GetMenuParams{ChannelCode: opts.ChannelCode, StoreID: opts.StoreID, Lang: opts.Lang, PartnerID: opts.PartnerID}, WithGetFunc(getFunc), WithSaveFunc(saveFunc), WithMaxRetries(opts.MaxRetries))
}

func getMenuDataFromProd(ctx context.Context, opts *GetMenuDataOptions) (*model.ProductListResponse, error) {
	// 实现仅从生产目录获取数据的逻辑
	getFunc := DefaultGetFuncWithBuffer(opts.ProdDir)
	saveFunc := DefaultSaveFuncWithBuffer(opts.ProdDir)
	GlobalMutex.Lock()
	defer GlobalMutex.Unlock()
	data, err := loadLocalMenuData(ctx, &pullDataOption{getFunc: getFunc, saveFunc: saveFunc})
	if err != nil {
		return nil, err
	}

	// 价格数据重新存储
	//go func() {
	//	bs, err := json.Marshal(data.PriceInfo)
	//	if err != nil {
	//		logger.Errorf("价格数据序列化失败: %v", err)
	//		return
	//	}
	//	err = saveFunc(bs, DefaultPriceFileName)
	//	if err != nil {
	//		logger.Errorf("价格数据保存失败: %v", err)
	//		return
	//	}
	//}()
	return getChannelProductList(ctx, opts.ChannelCode, data)
}

func fetchToTemp(ctx context.Context, opts *GetMenuDataOptions) error {
	// 实现仅拉取到暂存目录的逻辑
	getFunc := DefaultGetFuncWithBuffer(opts.ProdDir)
	saveFunc := DefaultSaveFuncWithBuffer(opts.TempDir)

	params := &GetMenuParams{
		ChannelCode: opts.ChannelCode,
		Lang:        opts.Lang,
		StoreID:     opts.StoreID,
	}
	GlobalMutex.Lock()
	defer GlobalMutex.Unlock()
	//检查暂存目录是否有更新
	hasUpdate, err := hasTempUpdate(opts)
	if err != nil {
		return err
	}
	if hasUpdate {
		getFunc = DefaultGetFuncWithBuffer(opts.TempDir)
	}
	data, err := loadLocalMenuData(ctx, &pullDataOption{getFunc: getFunc, saveFunc: saveFunc})
	if err != nil {
		return err
	}
	params.localData = data
	_, err = PullBaseData(ctx, opts.Host, opts.Token, params, WithGetFunc(getFunc), WithSaveFunc(saveFunc))
	if err != nil {
		logger.Errorf("云端数据拉取失败:云端错误=%v", err)
		return err
	}
	return nil
}

func mergeTempToProd(ctx context.Context, opts *GetMenuDataOptions) error {
	// 判断暂存目录是否有更新
	GlobalMutex.Lock()
	defer GlobalMutex.Unlock()
	hasUpdate, err := hasTempUpdate(opts)
	if err != nil {
		return err
	}
	// 如果没有更新，则直接返回`
	if !hasUpdate {
		return nil
	}
	// 全量覆盖的文件直接通过覆盖 避免重复marshal
	err = overrideFile(ctx, opts)
	if err != nil {
		return err
	}
	return nil
}

func hasTempUpdate(opts *GetMenuDataOptions) (bool, error) {
	// 检查暂存目录是否有更新
	if opts.TempDir == "" {
		return false, nil
	}

	// 读取暂存目录下的文件
	files, err := os.ReadDir(opts.TempDir)
	if err != nil {
		if os.IsNotExist(err) {
			// 如果目录不存在，则认为没有更新
			return false, nil
		}
		// 其他错误则返回错误
		return false, err
	}

	// 如果目录中有文件，则认为有更新
	return len(files) > 0, nil
}

func overrideFile(ctx context.Context, opts *GetMenuDataOptions) error {
	fileNameList := []string{
		DefaultProductListFileName,
		DefaultProductAttrFileName,
		DefaultPriceTypeFileName,
		DefaultPriceFileName,
		DefaultProductInfoFileName,
	}
	//检查prod 目录是否存在
	if _, err := os.Stat(opts.ProdDir); os.IsNotExist(err) {
		os.MkdirAll(opts.ProdDir, os.ModePerm)
	}
	for _, fileName := range fileNameList {
		tmpFilePath := filepath.Join(opts.TempDir, fileName)
		_, err := os.Stat(tmpFilePath)
		if err != nil {
			if os.IsNotExist(err) {
				continue
			}
			return err
		}
		prodFilePath := filepath.Join(opts.ProdDir, fileName)
		err = os.Rename(tmpFilePath, prodFilePath)
		if err != nil {
			return err
		}
		// 清理缓存
		ClearFileCache(prodFilePath)
	}
	return nil
}
