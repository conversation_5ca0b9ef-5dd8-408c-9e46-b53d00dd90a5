package omnichannel_product_list

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/buger/jsonparser"
	"github.com/goccy/go-json"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
)

// GetMenuParams 获取菜单参数（为了向后兼容保留此结构）
type GetMenuParams struct {
	StoreID     string          // storeID: 店铺ID
	Lang        string          // lang: 语言
	ChannelCode string          // channelCode: 渠道代码
	queryType   PriceQueryType  // queryType: 价格查询类型
	batchID     string          // batchID: 批次ID
	localData   *productListOpt `json:"local_data"`
	PartnerID   string
}

// PriceQueryType 价格查询类型
type PriceQueryType int

const (
	// PriceQueryTypeCurrent 查询当前价格 modify信息不返回
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify 查询Modify 仅返回最外层Modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll 查询所有
	PriceQueryTypeAll PriceQueryType = 3
)

var PriceQueryTypeMap = map[string]PriceQueryType{
	"PriceQueryTypeCurrent": PriceQueryTypeCurrent,
	"PriceQueryTypeModify":  PriceQueryTypeModify,
	"PriceQueryTypeAll":     PriceQueryTypeAll,
}

// PriceRequest 价格查询请求
type BaseDataRequest struct {
	BatchID string `json:"batchId"`
	Channel string `json:"channel"`
	StoreID string `json:"storeId"`
	// 查询类型 0:当前价格 2:modify 3:所有
	QueryType               string `json:"queryType"`
	OriginProductListMd5    string `json:"originProductListMd5"`
	OriginProductSpuInfoMd5 string `json:"originProductSpuInfoMd5"`
	OriginAttrListMd5       string `json:"originAttrListMd5"`
	OriginPriceTypeMd5      string `json:"originPriceTypeMd5"`
}

// Page 分页信息
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// 默认重试配置
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)

type pullDataOption struct {
	getFunc          func(string) ([]byte, error)
	saveFunc         func([]byte, string) error
	expireInterval   time.Duration
	maxRetries       int
	retryDelay       time.Duration
	httpClient       *http.Client
	returnBaseData   bool
	notFilterByPrice bool
}

// 全局HTTP客户端，用于复用连接
var defaultHTTPClient = &http.Client{
	Timeout: 30 * time.Second,
}

type PullDataOption func(*pullDataOption)

func WithGetFunc(getFunc func(string) ([]byte, error)) PullDataOption {
	return func(o *pullDataOption) {
		o.getFunc = getFunc
	}
}

func WithSaveFunc(saveFunc func([]byte, string) error) PullDataOption {
	return func(o *pullDataOption) {
		o.saveFunc = saveFunc
	}
}

func WithExpireInterval(expireInterval time.Duration) PullDataOption {
	return func(o *pullDataOption) {
		o.expireInterval = expireInterval
	}
}

func WithMaxRetries(maxRetries int) PullDataOption {
	return func(o *pullDataOption) {
		o.maxRetries = maxRetries
	}
}

func WithRetryDelay(retryDelay time.Duration) PullDataOption {
	return func(o *pullDataOption) {
		o.retryDelay = retryDelay
	}
}

func WithHttpClient(client *http.Client) PullDataOption {
	return func(o *pullDataOption) {
		o.httpClient = client
	}
}
func WithReturnBaseData(returnBaseData bool) PullDataOption {
	return func(o *pullDataOption) {
		o.returnBaseData = returnBaseData
	}
}
func WithNotFilterByPrice(notFilterByPrice bool) PullDataOption {
	return func(o *pullDataOption) {
		o.notFilterByPrice = notFilterByPrice
	}
}

// sendPriceRequest 发送价格查询请求并处理重试逻辑
// url: 请求URL
// req: 请求体
// token: 认证令牌
// maxRetries: 最大重试次数
// retryDelay: 重试间隔时间
func sendBaseDataRequest(ctx context.Context, host string, token string, params *GetMenuParams, opts *pullDataOption) ([]byte, error) {
	// 序列化请求体
	req := &BaseDataRequest{
		BatchID:                 params.batchID,
		Channel:                 params.ChannelCode,
		StoreID:                 params.StoreID,
		QueryType:               cast.ToString(int(params.queryType)),
		OriginProductListMd5:    *params.localData.OriginProductListMd5,
		OriginProductSpuInfoMd5: *params.localData.OriginProductSpuInfoMd5,
		OriginAttrListMd5:       *params.localData.OriginAttrListMd5,
		OriginPriceTypeMd5:      *params.localData.OriginPriceTypeMd5,
	}
	url := fmt.Sprintf("%s%s", host, DefaultBaseDataAPi)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 重试逻辑
	var respBody []byte
	var lastErr error

	for i := 0; i <= opts.maxRetries; i++ {
		// 如果不是第一次尝试，等待一段时间再重试
		if i > 0 {
			time.Sleep(opts.retryDelay)
		}
		// 创建HTTP请求
		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
		}

		// 设置请求头
		httpReq.Header.Set("Content-Type", "application/json")
		if token != "" {
			httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
		}
		// 设置语言
		if params.Lang != "" {
			httpReq.Header.Set("Accept-Language", params.Lang)
		}
		// 设置租户
		if params.PartnerID != "" {
			httpReq.Header.Set("partner_id", params.PartnerID)
		}

		respBody, err = getHttpResponse(httpReq, opts.httpClient)
		if err != nil {
			lastErr = err
			continue
		}
		return respBody, nil
	}

	// 所有重试都失败了，返回最后一个错误
	return nil, lastErr
}

// PullBaseData 从API拉取菜单基础信息
// host: API主机地址
// token: 认证令牌
// storeID: 店铺ID
// saveFunc: 保存价格信息的函数
// getFunc: 获取原有价格信息的函数

func PullBaseData(ctx context.Context, host string, token string, params *GetMenuParams, options ...PullDataOption) (*productListOpt, error) {

	currentPriceInfo := params.localData.PriceInfo
	var err error
	// 应用选项配置
	opts := applyPullPriceOptions(options...)

	if currentPriceInfo == nil {
		// 读取原有价格信息
		bs, err := opts.getFunc(DefaultPriceFileName)
		if err != nil {
			return nil, errors.Join(errors.New("读取原有价格信息失败: "), err)
		}

		if len(bs) > 0 {
			if err = json.Unmarshal(bs, currentPriceInfo); err != nil {
				return nil, errors.Join(errors.New("反序列化原有价格信息失败: "), err)
			}
		}
	}

	// 获取批次ID和查询类型
	params.batchID, params.queryType, err = getBatchIdAndQueryType(currentPriceInfo, opts.expireInterval)
	if err != nil {
		logger.Errorf("获取批次ID和查询类型失败: %v,改为全量抓取（可能为首次拉取）", err)
	}

	start := time.Now()
	respBody, err := sendBaseDataRequest(ctx, host, token, params, opts)
	if err != nil {
		return nil, err
	}
	logger.Infof("抓取基础云端数据: baseData，大小: %s,耗时:%v", formatDataSize(len(respBody)), time.Since(start))

	priceInfo, hasUpdated, err := buildPriceInfoByResponse(respBody, currentPriceInfo, params.queryType)
	if err != nil {
		return nil, err
	}
	if hasUpdated {
		priceInfoBs, err := json.Marshal(priceInfo)
		if err != nil {
			return nil, err
		}
		if err := opts.saveFunc(priceInfoBs, DefaultPriceFileName); err != nil {
			return nil, fmt.Errorf("保存价格信息失败: %w", err)
		}
	}

	params.localData, err = buildAndSaveBaseData(ctx, params.localData, respBody, opts.saveFunc)
	if err != nil {
		return nil, err
	}
	params.localData.PriceInfo = priceInfo
	return params.localData, nil
}

// GetPriceInfoFromFile 从文件中获取价格信息

func GetMenuData(ctx context.Context, host string, token string, params *GetMenuParams, options ...PullDataOption) (*model.ProductListResponse, error) {

	// 应用选项配置
	opts := applyPullPriceOptions(options...)

	var menuOpts *productListOpt
	var err error

	logger.Infof("开始获取菜单数据 - 店铺ID: %s, 渠道: %s", params.StoreID, params.ChannelCode)

	// 先尝试从云端拉取数据
	start := time.Now()
	//{
	//    "batchId": "5020927675187920896",
	//    "channel": "POS",
	//    "storeId": "4972806615293067264",
	//    "queryType": "2",
	//    "originProductListMd5":"89de39d677222fbb2f278053146023d4",
	//    "originProductSpuInfoMd5":"185e2bf66567f6bc9efc651436da0406",
	//    "originAttrListMd5":"4aa46649699733c0edf6386cc5e6523b",
	//    "originPriceTypeMd5":"a291c2d9a884075147337328a12fddb6"
	//}
	params.localData, err = loadLocalMenuData(ctx, opts)
	logger.Infof("loadLocalMenuData耗时：%v", time.Since(start))
	if err != nil {
		logger.Errorf("本地缓存读取失败:本地错误=%v", err)
	}
	menuOpts, err = PullBaseData(ctx, host, token, params, options...)
	elapsed := time.Since(start)
	logger.Infof("云端数据拉取耗时: %s", elapsed.String())
	if err != nil {
		logger.Warnf("拉取云端数据失败: %v, 尝试使用本地缓存数据", err)
		// 使用err group并发读取本地数据
		menuOpts = params.localData
		logger.Info("成功使用本地缓存数据")
	} else {
		logger.Info("成功从云端获取数据")
	}

	// 生成渠道产品列表
	start = time.Now()
	menuOpts.notFilterByPrice = opts.notFilterByPrice
	result, err := getChannelProductList(ctx, params.ChannelCode, menuOpts)
	logger.Infof("拼接耗时：%v", time.Since(start))
	if err != nil {
		return nil, fmt.Errorf("生成渠道产品列表失败: %w", err)
	}
	if opts.returnBaseData {
		result.BaseData = &model.BaseDataResponse{
			PriceInfo:     menuOpts.PriceInfo,
			PriceTypeInfo: menuOpts.PriceTypeInfo,
			ProductAttr:   menuOpts.ProductAttr,
			ProductInfo:   menuOpts.ProductInfo,
			ProductList:   menuOpts.ProductList,
			StockList:     menuOpts.StockList,
		}
	}
	return result, nil
}

// applyPullPriceOptions 应用拉取价格选项配置
func applyPullPriceOptions(options ...PullDataOption) *pullDataOption {
	opts := &pullDataOption{
		maxRetries:     DefaultMaxRetries,
		retryDelay:     DefaultRetryDelay,
		expireInterval: DefaultExpireInterval,
		getFunc:        DefaultGetFunc("./"),
		saveFunc:       DefaultSaveFunc("./"),
		httpClient:     defaultHTTPClient,
	}
	for _, option := range options {
		option(opts)
	}
	return opts
}

// 定义文件读取任务
type fileTask struct {
	fileName        string
	unmarshalTarget any
	name            string
	md5String       *string
}

// loadLocalMenuData 并发加载本地菜单数据
func loadLocalMenuData(ctx context.Context, opts *pullDataOption) (*productListOpt, error) {
	menuOpts := &productListOpt{
		ProductList:             make([]*model.Category, 0),
		ProductInfo:             make([]*model.Product, 0),
		ProductAttr:             make([]*model.AdditionalAttribute, 0),
		PriceInfo:               &model.PriceInfo{},
		PriceTypeInfo:           make([]*model.PriceType, 0),
		StockList:               make([]map[string]interface{}, 0),
		OriginPriceTypeMd5:      new(string),
		OriginProductSpuInfoMd5: new(string),
		OriginAttrListMd5:       new(string),
		OriginProductListMd5:    new(string),
		OriginStockListMd5:      new(string),
	}
	g, ctx := errgroup.WithContext(ctx)
	tasks := []fileTask{
		{DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "price_type", menuOpts.OriginPriceTypeMd5},
		{DefaultProductInfoFileName, &menuOpts.ProductInfo, "product_info", menuOpts.OriginProductSpuInfoMd5},
		{DefaultProductAttrFileName, &menuOpts.ProductAttr, "product_attr", menuOpts.OriginAttrListMd5},
		{DefaultProductListFileName, &menuOpts.ProductList, "product_list", menuOpts.OriginProductListMd5},
		{DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}

	// 单独处理价格信息
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			data, err := GetPriceInfoFromFile(0, opts.getFunc, opts.saveFunc)
			if err != nil {
				return fmt.Errorf("读取%s文件失败: %w", "price", err)
			}
			menuOpts.PriceInfo = data
			//		logger.Infof("成功读取本地文件: price, 大小: %s", formatDataSize(len(data)))
			return nil
		}
	})

	// 并发读取所有本地文件
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				start := time.Now()
				data, err := opts.getFunc(task.fileName)
				if err != nil {
					return fmt.Errorf("读取%s文件失败: %w", task.name, err)
				}
				md5, _ := jsonparser.GetString(data, "md5")
				*task.md5String = md5
				data, _, _, _ = jsonparser.Get(data, "rows")
				if len(data) == 0 {
					logger.Infof("本地文件%s为空,跳过", task.name)
					return nil
				}
				err = json.Unmarshal(data, task.unmarshalTarget)
				if err != nil {
					return fmt.Errorf("解析%s文件失败: %w", task.name, err)
				}
				logger.Infof("成功读取本地文件: %s, 大小: %s,耗时：%v", task.name, formatDataSize(len(data)), time.Since(start))
				return nil
			}
		})
	}

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("并发读取本地文件失败: %w", err)
	}

	logger.Info("所有本地文件读取完成")
	return menuOpts, nil
}

// 获取并其他信息（product_info product_attr price_type product_list）返回map[string][]byte 文件名和内容
func buildAndSaveBaseData(ctx context.Context, menuOpts *productListOpt, resp []byte, savefunc func([]byte, string) error) (*productListOpt, error) {
	if menuOpts == nil {
		menuOpts = &productListOpt{
			ProductList:   make([]*model.Category, 0),
			ProductInfo:   make([]*model.Product, 0),
			ProductAttr:   make([]*model.AdditionalAttribute, 0),
			PriceInfo:     &model.PriceInfo{},
			PriceTypeInfo: make([]*model.PriceType, 0),
		}
	}
	tasks := []fileTask{
		{DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "priceType", menuOpts.OriginPriceTypeMd5},
		{DefaultProductInfoFileName, &menuOpts.ProductInfo, "productSpuInfo", menuOpts.OriginProductSpuInfoMd5},
		{DefaultProductAttrFileName, &menuOpts.ProductAttr, "attrList", menuOpts.OriginAttrListMd5},
		{DefaultProductListFileName, &menuOpts.ProductList, "productList", menuOpts.OriginProductListMd5},
		{DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}
	g, ctx := errgroup.WithContext(ctx)
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				data, _, _, err := jsonparser.Get(resp, "payload", task.name)
				if err != nil {
					return fmt.Errorf("获取%s失败: %w", task.name, err)
				}
				rows, _, _, err := jsonparser.Get(data, "rows")
				if err != nil {
					return fmt.Errorf("获取%s.rows失败: %w", task.name, err)
				}
				if cast.ToString(rows) == "null" {
					logger.Infof("%s:本地数据和云端数据一致，使用本地数据", task.name)
					return nil
				}
				err = json.Unmarshal(rows, task.unmarshalTarget)
				logger.Infof("成功读取云端数据: %s, 大小: %s", task.name, formatDataSize(len(data)))
				if err := savefunc(data, task.fileName); err != nil {
					return fmt.Errorf("保存%s失败: %w", task.name, err)
				}
				return nil
			}
		})
	}
	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("并发读取云端数据失败: %w", err)
	}

	return menuOpts, nil
}
