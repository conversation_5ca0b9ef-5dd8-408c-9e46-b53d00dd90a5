package omnichannel_product_list

import (
	"strings"

	"github.com/huandu/go-clone"
	"github.com/spf13/cast"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
)

// 过滤菜单并填充价格信息
// 整行数据无效时返回nil
func filterAndFillPriceInfo(product *model.Product, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeMap map[string]string, priceTypeID string, notFilter bool) (p *model.Product, err error) {
	// 一次性克隆整个Product对象，确保所有嵌套对象都被正确克隆
	// 这样避免了在各个子函数中分别克隆，提高了性能和代码一致性
	if product != nil {
		p = clone.Clone(product).(*model.Product)
	} else {
		return nil, nil
	}

	relation := p.Relation

	//推荐商品
	//recommendGroupList, ok := product["recommend_group_list"].([]interface{})
	recommendGroupList := p.RecommendGroupList

	recommendGroupList = filterRecommendGroupList(recommendGroupList, priceInfoMap, priceTypeID, priceTypeMap, notFilter)
	p.RecommendGroupList = recommendGroupList

	setType := product.SetType

	// sku价格字段为selling_price
	// skuCodeFunc, "code", "", "selling_price", "", false
	relation.Sku = filterSkuByItemCode(setType, relation.Sku, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
		CodeFunc:  skuCodeFunc,
		CodeKey:   "code",
		PriceKey:  "selling_price",
		NotFilter: notFilter,
	})

	if len(relation.Sku) == 0 {
		p = nil
		return
	}
	p.ChannelItemPriceMap = relation.Sku[0].ChannelItemPriceMap

	// 加料过滤
	relation.Addition = filterAddition(relation.Addition, priceInfoMap, priceTypeID, priceTypeMap, notFilter)

	//商品属性过滤

	relation.AdditionalAttribute = filterAdditionalAttribute(relation.AdditionalAttribute, priceInfoMap, priceTypeID, priceTypeMap, notFilter)

	// 套餐商品过滤
	// 商品中是否包含主商品

	basicSetSubitemId := product.BasicSetSubitemId

	relation.Set = filterSet(setType, relation.Set, priceInfoMap, priceTypeID, priceTypeMap, basicSetSubitemId, notFilter)

	//六、二级属性过滤:
	//productList-->>relation-->>secondLevelAttrList-->>attribute_values-->>code

	relation.SecondLevelAttrList = filterSecondLevelAttrList(relation.SecondLevelAttrList, priceInfoMap, priceTypeID, priceTypeMap, notFilter)

	return
}

// 加料过滤
func filterAddition(additionArray []*model.Addition, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, notFilter bool) []*model.Addition {
	// 移除函数内部的克隆操作，因为已经在filterAndFillPriceInfo中进行了完整克隆
	// 加料过滤
	// 加料的价格字段为raise_price
	//  defaultCodeFunc, "modifyCode", "", "raise_price", "", false
	additionArray = filterAdditionByItemCode(additionArray, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
		CodeFunc:  defaultCodeFunc,
		CodeKey:   "modifyCode",
		PriceKey:  "raise_price",
		NotFilter: notFilter,
	})
	filterAdditionArrayByGroupModifyCode := make([]*model.Addition, 0, len(additionArray))

	//defaultCodeFunc, "groupModifyCode", "", "groupFreePrice", "groupFreeItemPriceMap", true
	cfg := &FilterConfig{
		CodeFunc:        defaultCodeFunc,
		CodeKey:         "groupModifyCode",
		PriceKey:        "groupFreePrice",
		ChannelPriceKey: "groupFreeItemPriceMap",
		NotFilter:       true,
	}
	for _, source := range additionArray {
		code := source.GroupModifyCode
		itemCode := cfg.CodeFunc(code)
		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !cfg.NotFilter {
			continue
		}
		source.GroupFreePrice = price
		source.GroupFreeItemPriceMap = channelItemPriceMap
		filterAdditionArrayByGroupModifyCode = append(filterAdditionArrayByGroupModifyCode, source)
	}

	//加料属性过滤
	// 价格字段: price
	cfg = nil
	cfg = &FilterConfig{
		CodeFunc:  defaultCodeFunc,
		CodeKey:   "code",
		PriceKey:  "price",
		NotFilter: notFilter,
	}
	filterAdditionArrayByAttrValue := make([]*model.Addition, 0, len(filterAdditionArrayByGroupModifyCode))
	for _, addition := range filterAdditionArrayByGroupModifyCode {
		if addition != nil {
			filterAdditionalAttributeArray := make([]*model.AdditionalAttribute, 0, len(addition.AdditionalAttribute))
			for _, attr := range addition.AdditionalAttribute {
				filterAttributeValuesArray := make([]*model.AttributeValues, 0, len(attr.AttributeValues))
				for _, attrValue := range attr.AttributeValues {
					code := attrValue.Code
					itemCode := cfg.CodeFunc(code)
					price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
					if !canPass && !cfg.NotFilter {
						continue
					}
					attrValue.Price = price
					attrValue.ChannelItemPriceMap = channelItemPriceMap
					filterAttributeValuesArray = append(filterAttributeValuesArray, attrValue)
				}
				attr.AttributeValues = filterAttributeValuesArray
				filterAdditionalAttributeArray = append(filterAdditionalAttributeArray, attr)
			}
			filterAdditionArrayByAttrValue = append(filterAdditionArrayByAttrValue, addition)
		}
	}
	return filterAdditionArrayByAttrValue
}

// 商品属性
// 价格字段:  price
func filterAdditionalAttribute(additionalAttributeArray []*model.AdditionalAttribute, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, notFilter bool) []*model.AdditionalAttribute {
	// 移除函数内部的克隆操作，因为已经在filterAndFillPriceInfo中进行了完整克隆
	filterAdditionalAttributeArray := make([]*model.AdditionalAttribute, 0, len(additionalAttributeArray))
	cfg := &FilterConfig{
		CodeFunc:  defaultCodeFunc,
		CodeKey:   "code",
		PriceKey:  "price",
		NotFilter: notFilter,
	}
	for _, attribute := range additionalAttributeArray {
		if attribute == nil {
			continue
		}
		filterAttributeValuesArray := make([]*model.AttributeValues, 0, len(attribute.AttributeValues))
		for _, attrValue := range attribute.AttributeValues {
			code := attrValue.Code
			itemCode := cfg.CodeFunc(code)
			price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
			if !canPass && !cfg.NotFilter {
				continue
			}
			attrValue.Price = price
			attrValue.ChannelItemPriceMap = channelItemPriceMap
			filterAttributeValuesArray = append(filterAttributeValuesArray, attrValue)
		}
		attribute.AttributeValues = filterAttributeValuesArray
		filterAdditionalAttributeArray = append(filterAdditionalAttributeArray, attribute)
	}
	return filterAdditionalAttributeArray
}

// 套餐子商品
func filterSet(setType string, setArray []*model.Set, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, basicSetSubitemId string, notFilter bool) []*model.Set {
	// 移除函数内部的克隆操作，因为已经在filterAndFillPriceInfo中进行了完整克隆

	//  1、productList-->>relation-->>set-->>modifyCode
	//价格字段: raisePrice
	filterSetArray := filterArrayByComboItemCode(setArray, priceInfoMap, priceTypeID, priceTypeMap, defaultCodeFunc, "modifyCode", "", "tmpRaisePrice", "", basicSetSubitemId, notFilter)
	resultSetArray := make([]*model.Set, 0, len(filterSetArray))
	for _, set := range filterSetArray {

		////subItemSpuMap, ok := setMap["subItemSpu"].(map[string]interface{})
		if set.SubItemSpu == nil {
			continue
		}
		relation := set.SubItemSpu.Relation
		if relation == nil {
			continue
		}

		//skuArray, ok := relationMap["sku"].([]interface{})
		if len(relation.Sku) == 0 {
			continue
		}

		comboItemId := set.ProductId
		for _, sku := range relation.Sku {
			if comboItemId == sku.Id {
				if setType != FLEX_SET {
					sku.ChannelItemPriceMap = set.ChannelItemPriceMap
					sku.SellingPrice = set.TmpRaisePrice
					//skuMap["channelItemPriceMap"] = setMap["channelItemPriceMap"]
					//skuMap["selling_price"] = setMap["tmpRaisePrice"]
				} else {
					sku.OriginalPrice = set.TmpRaisePrice
					sku.SellingPrice = set.RaisePrice
					sku.ChannelItemPriceMap = set.ChannelItemPriceMap
					//skuMap["original_price"] = setMap["tmpRaisePrice"]
					//skuMap["channelItemPriceMap"] = setMap["channelItemPriceMap"]
					//skuMap["selling_price"] = setMap["raisePrice"]
					set.ChannelItemPriceMap = nil
				}
			}
		}
		set.TmpRaisePrice = 0
		//delete(setMap, "tmpRaisePrice")

		//2、套餐子商品的加料
		//productList-->>relation-->>set-->>subItemSpu-->>relation-->>addition-->>modifyCode
		relation.Addition = filterAddition(relation.Addition, priceInfoMap, priceTypeID, priceTypeMap, notFilter)

		// 3、套餐子商品的属性
		//    productList-->>relation-->>set-->>subItemSpu-->>relation-->>additional_attribute-->>attribute_values-->>code

		relation.AdditionalAttribute = filterAdditionalAttribute(relation.AdditionalAttribute, priceInfoMap, priceTypeID, priceTypeMap, notFilter)
		resultSetArray = append(resultSetArray, set)
	}
	return resultSetArray

}

// 推荐商品:
func filterRecommendGroupList(recommendGroupList []*model.RecommendGroup, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, notFilter bool) []*model.RecommendGroup {
	// 移除函数内部的克隆操作，因为已经在filterAndFillPriceInfo中进行了完整克隆
	filterRecommendGroupList := make([]*model.RecommendGroup, 0, len(recommendGroupList))
	for _, recommendGroup := range recommendGroupList {
		if recommendGroup == nil {
			continue
		}

		// defaultCodeFunc, "modifyCode", "code", "price", "", false
		recommendGroup.Items = filterRecommendItems(recommendGroup.Items, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
			CodeFunc:       defaultCodeFunc,
			CodeKey:        "modifyCode",
			DefaultCodeKey: "code",
			PriceKey:       "price",
		})
		filterItems := make([]*model.RecommendItem, 0, len(recommendGroup.Items))
		for _, item := range recommendGroup.Items {

			if item == nil {
				continue
			}
			if item.ChannelProductVO == nil {
				continue
			}
			relation := item.ChannelProductVO.Relation
			if relation == nil {
				continue
			}
			//  2、推荐商品的加料
			//additionArray, ok := relationMap["addition"].([]interface{})
			if len(relation.Addition) == 0 {
				continue
			}
			relation.Addition = filterAddition(relation.Addition, priceInfoMap, priceTypeID, priceTypeMap, notFilter)

			//推荐商品的属性
			if len(relation.AdditionalAttribute) == 0 {
				continue
			}
			relation.AdditionalAttribute = filterAdditionalAttribute(relation.AdditionalAttribute, priceInfoMap, priceTypeID, priceTypeMap, notFilter)
			filterItems = append(filterItems, item)
		}
		recommendGroup.Items = filterItems
		filterRecommendGroupList = append(filterRecommendGroupList, recommendGroup)
	}
	return filterRecommendGroupList
}

// 二级属性过滤:
// productList-->>relation-->>secondLevelAttrList-->>attribute_values-->>code
func filterSecondLevelAttrList(secondLevelAttrList []*model.AdditionalAttribute, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, notFilter bool) []*model.AdditionalAttribute {
	filterSecondLevelAttrList := make([]*model.AdditionalAttribute, 0, len(secondLevelAttrList))
	for _, secondLevelAttr := range secondLevelAttrList {
		if secondLevelAttr == nil {
			continue
		}
		secondLevelAttr.AttributeValues = filterAttributeValuesArray(secondLevelAttr.AttributeValues, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
			CodeFunc:  defaultCodeFunc,
			CodeKey:   "code",
			PriceKey:  "price",
			NotFilter: notFilter,
		})
		filterSecondLevelAttrList = append(filterSecondLevelAttrList, secondLevelAttr)
	}
	return filterSecondLevelAttrList
}

// 获取当前价格和所有渠道价格
func getCurrentPriceAndChannelPrice(AllTypePriceMap map[string]*model.PriceAgent, mainPriceTypeID string, priceTypeMap map[string]string) (float64, map[string]float64) {
	channelItemPriceMap := make(map[string]float64, len(AllTypePriceMap))
	var currentPrice float64
	for priceTypeID, priceInfo := range AllTypePriceMap {
		price := cast.ToFloat64(priceInfo.Price)
		channelCode := priceTypeMap[priceTypeID]
		channelItemPriceMap[channelCode] = price
		if priceTypeID == mainPriceTypeID {
			currentPrice = price
		}
	}
	return currentPrice, channelItemPriceMap
}

// 根据item判断是否下架，且返回价格信息（当前渠道价格，所有渠道价格）
// ture 表示有效 fasle 表示无效
func filterAndGetPriceInfoByItemCode(itemCode string, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string) (float64, map[string]float64, bool) {
	AllTypePriceMap, ok := priceInfoMap[itemCode]
	if !ok {
		return 0, nil, false
	}
	priceAgent, ok := AllTypePriceMap[priceTypeID]
	if !ok {
		return 0, nil, false
	}
	// 是否下架
	if priceAgent.Hidden {
		return 0, nil, false
	}
	currentPrice, channelItemPriceMap := getCurrentPriceAndChannelPrice(AllTypePriceMap, priceTypeID, priceTypeMap)
	//if currentPrice == PriceUnavailable {
	//	return 0, nil, false
	//}
	return currentPrice, channelItemPriceMap, true
}

// 过滤数据切片
// sourceArray 是包含商品code 的切片数据，不支持多层嵌套
// codeFunc: 用于从商品code中提取实际的itemCode
// codeKey: 用于从sourceMap中提取商品code的键
// defaultCodeKey: 用于从sourceMap中提取默认商品code的键，当codeKey为空时使用
// priceKey: 用于在sourceMap中设置价格的键
// channelPriceKey: 用于在sourceMap中设置所有渠道价格的键
// notFilter: 是否不需要过滤
type FilterConfig struct {
	CodeFunc          func(string) string
	CodeKey           string
	DefaultCodeKey    string
	PriceKey          string
	ChannelPriceKey   string
	NotFilter         bool
	BasicSetSubitemId string // 仅用于ComboItem
}

func filterArrayByItemCode(sourceArray []interface{}, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, filters *FilterConfig) []interface{} {
	if filters.CodeKey == "" {
		return nil
	}
	// 所有渠道价格默认为channelItemPriceMap
	if filters.ChannelPriceKey == "" {
		filters.ChannelPriceKey = "channelItemPriceMap"
	}

	filerArray := make([]interface{}, 0, len(sourceArray))
	for _, source := range sourceArray {
		sourceMap, ok := source.(map[string]interface{})
		if !ok {
			continue
		}
		code := cast.ToString(sourceMap[filters.CodeKey])
		itemCode := filters.CodeFunc(code)
		//if itemCode == "" {
		//	continue
		//}
		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !filters.NotFilter {
			if filters.DefaultCodeKey != "" {
				code, ok := sourceMap[filters.DefaultCodeKey].(string)
				if !ok {
					continue
				}
				price, channelItemPriceMap, canPass = filterAndGetPriceInfoByItemCode(code, priceInfoMap, priceTypeID, priceTypeMap)
				if !canPass {
					continue
				}
			}
			continue
		}
		// todo 可售时段
		sourceMap[filters.PriceKey] = price
		sourceMap[filters.ChannelPriceKey] = channelItemPriceMap
		filerArray = append(filerArray, sourceMap)
	}
	return filerArray
}

// 过滤数据切片
// sourceArray 是包含商品code 的切片数据，不支持多层嵌套
// codeFunc: 用于从商品code中提取实际的itemCode
// codeKey: 用于从sourceMap中提取商品code的键
// defaultCodeKey: 用于从sourceMap中提取默认商品code的键，当codeKey为空时使用
// priceKey: 用于在sourceMap中设置价格的键
// channelPriceKey: 用于在sourceMap中设置所有渠道价格的键
// notFilter: 是否不需要过滤
//
//	filterSetArray := filterArrayByComboItemCode(setArray, priceInfoMap, priceTypeID, priceTypeMap, defaultCodeFunc, "modifyCode", "", "tmpRaisePrice", "", basicSetSubitemId, false)
func filterArrayByComboItemCode(sourceArray []*model.Set, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, codeFunc func(string) string, codeKey string, defaultCodeKey string, priceKey string, channelPriceKey string, basicSetSubitemId string, notFilter bool) []*model.Set {

	filerArray := make([]*model.Set, 0, len(sourceArray))
	for _, source := range sourceArray {
		code := source.ModifyCode
		itemCode := codeFunc(code)

		itemId := source.ProductId
		notFilter = strings.Contains(basicSetSubitemId, itemId)

		if itemCode == "" && !notFilter {
			continue
		}

		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !notFilter {
			continue
		}
		// todo 可售时段
		source.TmpRaisePrice = price
		source.ChannelItemPriceMap = channelItemPriceMap
		filerArray = append(filerArray, source)
	}
	return filerArray
}

var defaultCodeFunc = func(code string) string {
	return code
}

func skuCodeFunc(code string) string {
	return strings.Split(strings.Split(code, "UNION")[0], "union")[0]
}

// sku价格字段为selling_price
// skuCodeFunc, "code", "", "selling_price", "", false
func filterSkuByItemCode(setType string, skuArray []*model.Sku, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, filters *FilterConfig) []*model.Sku {
	//if len(skuArray) != 0 {
	//	skuArray = clone.Clone(skuArray).([]*model.Sku)
	//}
	filerArray := make([]*model.Sku, 0, len(skuArray))
	for _, sku := range skuArray {
		code := sku.Code
		itemCode := filters.CodeFunc(code)
		if itemCode == "" {
			continue
		}
		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)

		if !canPass && !filters.NotFilter && setType != "FLEX_SET" {
			continue
		}
		// todo 可售时段
		if canPass {
			sku.SellingPrice = price
			sku.ChannelItemPriceMap = channelItemPriceMap
		}
		filerArray = append(filerArray, sku)
	}
	return filerArray
}

// 加料过滤
// 加料的价格字段为raise_price
//
//	defaultCodeFunc, "modifyCode", "", "raise_price", "", false
func filterAdditionByItemCode(sourceArray []*model.Addition, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, filters *FilterConfig) []*model.Addition {

	filerArray := make([]*model.Addition, 0, len(sourceArray))
	for _, source := range sourceArray {
		code := source.ModifyCode
		itemCode := filters.CodeFunc(code)
		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !filters.NotFilter {
			continue
		}
		source.RaisePrice = price
		source.ChannelItemPriceMap = channelItemPriceMap
		filerArray = append(filerArray, source)
	}
	return filerArray
}

//defaultCodeFunc, "groupModifyCode", "", "groupFreePrice", "groupFreeItemPriceMap", true
//additionArray = filterArrayByItemCode(additionArray, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
//CodeFunc:        defaultCodeFunc,
//CodeKey:         "groupModifyCode",
//PriceKey:        "groupFreePrice",
//ChannelPriceKey: "groupFreeItemPriceMap",
//NotFilter:       true,
//})

//	&FilterConfig{
//				CodeFunc: defaultCodeFunc,
//				CodeKey:  "code",
//				PriceKey: "price",
//			})
func filterAttributeValuesArray(sourceArray []*model.AttributeValues, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, filters *FilterConfig) []*model.AttributeValues {
	filerArray := make([]*model.AttributeValues, 0, len(sourceArray))
	for _, source := range sourceArray {

		code := source.Code
		itemCode := filters.CodeFunc(code)
		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !filters.NotFilter {
			continue
		}
		// todo 可售时段
		source.Price = price
		source.ChannelItemPriceMap = channelItemPriceMap
		filerArray = append(filerArray, source)
	}
	return filerArray
}

//	recommendGroup.Items = filterArrayByItemCode(recommendGroup.Items, priceInfoMap, priceTypeID, priceTypeMap, &FilterConfig{
//			CodeFunc:       defaultCodeFunc,
//			CodeKey:        "modifyCode",
//			DefaultCodeKey: "code",
//			PriceKey:       "price",
//		})
func filterRecommendItems(sourceArray []*model.RecommendItem, priceInfoMap map[string]map[string]*model.PriceAgent, priceTypeID string, priceTypeMap map[string]string, filters *FilterConfig) []*model.RecommendItem {

	filerArray := make([]*model.RecommendItem, 0, len(sourceArray))
	for _, source := range sourceArray {
		if source == nil {
			continue
		}
		code := source.ModifyCode
		itemCode := filters.CodeFunc(code)

		price, channelItemPriceMap, canPass := filterAndGetPriceInfoByItemCode(itemCode, priceInfoMap, priceTypeID, priceTypeMap)
		if !canPass && !filters.NotFilter {
			if filters.DefaultCodeKey != "" {
				code = source.Code
				price, channelItemPriceMap, canPass = filterAndGetPriceInfoByItemCode(code, priceInfoMap, priceTypeID, priceTypeMap)
				if !canPass {
					continue
				}
			}
			continue
		}
		// todo 可售时段
		source.Price = price
		source.ChannelItemPriceMap = channelItemPriceMap
		filerArray = append(filerArray, source)
	}
	return filerArray
}
