package omnichannel_product_list

import (
	"bufio"
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/goccy/go-json"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/buger/jsonparser"
	"golang.org/x/sync/errgroup"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
)

// 文件读取缓冲区大小
const bufferSize = 64 * 1024 // 64KB

// 内存缓存，用于存储最近读取的文件内容
var (
	fileCache      = make(map[string][]byte)
	fileCacheMutex sync.RWMutex
	cacheTTL       = 5 * time.Minute // 缓存5分钟
)

// 带缓冲的文件读取函数
func bufferedReadFile(fileName string) ([]byte, error) {
	// 首先检查缓存
	fileCacheMutex.RLock()
	if cached, exists := fileCache[fileName]; exists {
		fileCacheMutex.RUnlock()
		return cached, nil
	}
	fileCacheMutex.RUnlock()

	// 缓存未命中，从文件系统读取
	file, err := os.Open(fileName)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 使用缓冲读取器
	reader := bufio.NewReaderSize(file, bufferSize)
	var buffer bytes.Buffer

	// 读取文件内容
	_, err = io.CopyBuffer(&buffer, reader, make([]byte, bufferSize))
	if err != nil {
		return nil, err
	}

	// 更新缓存
	fileCacheMutex.Lock()
	fileCache[fileName] = buffer.Bytes()
	// 设置定时清理缓存
	time.AfterFunc(cacheTTL, func() {
		fileCacheMutex.Lock()
		delete(fileCache, fileName)
		fileCacheMutex.Unlock()
	})
	fileCacheMutex.Unlock()

	return buffer.Bytes(), nil
}

// 提供手动清理缓存功能
func ClearFileCache(fileName string) {
	fileCacheMutex.Lock()
	delete(fileCache, fileName)
	fileCacheMutex.Unlock()
}

// 带缓冲的文件写入函数
func bufferedWriteFile(fileName string, data []byte) error {
	// 确保目录存在
	dir := filepath.Dir(fileName)
	if dir != "" && dir != "." {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("创建目录失败: %w", err)
		}
	}

	// 创建文件
	file, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer file.Close()

	// 使用缓冲写入器
	writer := bufio.NewWriterSize(file, bufferSize)
	_, err = writer.Write(data)
	if err != nil {
		return err
	}

	// 确保数据写入磁盘
	return writer.Flush()
}

// 改进的DefaultGetFunc，使用带缓冲的文件读取
func DefaultGetFuncWithBuffer(dir string) func(string) ([]byte, error) {
	return func(fileName string) ([]byte, error) {
		savePath := getFilePath(dir, fileName)
		if _, err := os.Stat(savePath); os.IsNotExist(err) {
			return nil, nil
		}
		if savePath == "" {
			return nil, errors.New("保存路径不能为空")
		}
		// 使用带缓冲的文件读取
		return bufferedReadFile(savePath)
	}
}

// 改进的DefaultSaveFunc，使用带缓冲的文件写入
func DefaultSaveFuncWithBuffer(dir string) func([]byte, string) error {
	return func(data []byte, fileName string) error {
		savePath := getFilePath(dir, fileName)
		if savePath == "" {
			return errors.New("保存路径不能为空")
		}
		// 使用带缓冲的文件写入
		return bufferedWriteFile(savePath, data)
	}
}

// 改进的loadLocalMenuData函数，增加性能优化
func loadLocalMenuDataOptimized(ctx context.Context, opts *pullDataOption) (*productListOpt, error) {
	menuOpts := &productListOpt{
		ProductList:             make([]*model.Category, 0),
		ProductInfo:             make([]*model.Product, 0),
		ProductAttr:             make([]*model.AdditionalAttribute, 0),
		PriceInfo:               &model.PriceInfo{},
		PriceTypeInfo:           make([]*model.PriceType, 0),
		OriginPriceTypeMd5:      new(string),
		OriginProductSpuInfoMd5: new(string),
		OriginAttrListMd5:       new(string),
		OriginProductListMd5:    new(string),
	}

	// 使用通道来收集结果，避免并发写入共享内存
	priceTypeChan := make(chan []*model.PriceType, 1)
	productInfoChan := make(chan []*model.Product, 1)
	productAttrChan := make(chan []*model.AdditionalAttribute, 1)
	productListChan := make(chan []*model.Category, 1)

	priceTypeMd5Chan := make(chan string, 1)
	productInfoMd5Chan := make(chan string, 1)
	productAttrMd5Chan := make(chan string, 1)
	productListMd5Chan := make(chan string, 1)

	// 错误通道
	errChan := make(chan error, 5) // 5个goroutine，预留足够的缓冲

	g, ctx := errgroup.WithContext(ctx)

	// 单独处理价格信息
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			data, err := GetPriceInfoFromFile(0, opts.getFunc, opts.saveFunc)
			if err != nil {
				errChan <- fmt.Errorf("读取%s文件失败: %w", "price", err)
				return err
			}
			menuOpts.PriceInfo = data
			return nil
		}
	})

	// 并发读取价格类型文件
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultPriceTypeFileName)
			if err != nil {
				errChan <- fmt.Errorf("读取%s文件失败: %w", "price_type", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			priceTypeMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("本地文件%s为空,跳过", "price_type")
				priceTypeChan <- make([]*model.PriceType, 0)
				return nil
			}

			var priceTypes []*model.PriceType
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&priceTypes)
			err = json.Unmarshal(data, &priceTypes)
			if err != nil {
				errChan <- fmt.Errorf("解析%s文件失败: %w", "price_type", err)
				return err
			}

			logger.Infof("成功读取本地文件: %s, 大小: %s,耗时：%v", "price_type", formatDataSize(len(data)), time.Since(start))
			priceTypeChan <- priceTypes
			return nil
		}
	})

	// 并发读取商品信息文件
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductInfoFileName)
			if err != nil {
				errChan <- fmt.Errorf("读取%s文件失败: %w", "product_info", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productInfoMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("本地文件%s为空,跳过", "product_info")
				productInfoChan <- make([]*model.Product, 0)
				return nil
			}

			var products []*model.Product
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&products)
			err = json.Unmarshal(data, &products)
			if err != nil {
				errChan <- fmt.Errorf("解析%s文件失败: %w", "product_info", err)
				return err
			}

			logger.Infof("成功读取本地文件: %s, 大小: %s,耗时：%v", "product_info", formatDataSize(len(data)), time.Since(start))
			productInfoChan <- products
			return nil
		}
	})

	// 并发读取商品属性文件
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductAttrFileName)
			if err != nil {
				errChan <- fmt.Errorf("读取%s文件失败: %w", "product_attr", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productAttrMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("本地文件%s为空,跳过", "product_attr")
				productAttrChan <- make([]*model.AdditionalAttribute, 0)
				return nil
			}

			var attributes []*model.AdditionalAttribute
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&attributes)
			err = json.Unmarshal(data, &attributes)
			if err != nil {
				errChan <- fmt.Errorf("解析%s文件失败: %w", "product_attr", err)
				return err
			}

			logger.Infof("成功读取本地文件: %s, 大小: %s,耗时：%v", "product_attr", formatDataSize(len(data)), time.Since(start))
			productAttrChan <- attributes
			return nil
		}
	})

	// 并发读取商品列表文件
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			start := time.Now()
			data, err := opts.getFunc(DefaultProductListFileName)
			if err != nil {
				errChan <- fmt.Errorf("读取%s文件失败: %w", "product_list", err)
				return err
			}
			md5, _ := jsonparser.GetString(data, "md5")
			productListMd5Chan <- md5

			data, _, _, _ = jsonparser.Get(data, "rows")
			if len(data) == 0 {
				logger.Infof("本地文件%s为空,跳过", "product_list")
				productListChan <- make([]*model.Category, 0)
				return nil
			}

			var categories []*model.Category
			//dc := decoder.NewDecoder(string(data))
			//dc.UseNumber()
			//err = dc.Decode(&categories)
			err = json.Unmarshal(data, &categories)
			if err != nil {
				errChan <- fmt.Errorf("解析%s文件失败: %w", "product_list", err)
				return err
			}

			logger.Infof("成功读取本地文件: %s, 大小: %s,耗时：%v", "product_list", formatDataSize(len(data)), time.Since(start))
			productListChan <- categories
			return nil
		}
	})

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		close(errChan)
		// 收集所有错误
		var errMsgs []string
		for err := range errChan {
			errMsgs = append(errMsgs, err.Error())
		}
		if len(errMsgs) > 0 {
			return menuOpts, fmt.Errorf("并发读取本地文件失败: %s", strings.Join(errMsgs, "; "))
		}
		return menuOpts, fmt.Errorf("并发读取本地文件失败: %w", err)
	}

	// 关闭通道
	close(priceTypeChan)
	close(productInfoChan)
	close(productAttrChan)
	close(productListChan)
	close(priceTypeMd5Chan)
	close(productInfoMd5Chan)
	close(productAttrMd5Chan)
	close(productListMd5Chan)
	close(errChan)

	// 从通道中读取结果并赋值给menuOpts
	menuOpts.PriceTypeInfo = <-priceTypeChan
	menuOpts.ProductInfo = <-productInfoChan
	menuOpts.ProductAttr = <-productAttrChan
	menuOpts.ProductList = <-productListChan

	*menuOpts.OriginPriceTypeMd5 = <-priceTypeMd5Chan
	*menuOpts.OriginProductSpuInfoMd5 = <-productInfoMd5Chan
	*menuOpts.OriginAttrListMd5 = <-productAttrMd5Chan
	*menuOpts.OriginProductListMd5 = <-productListMd5Chan

	logger.Info("所有本地文件读取完成")
	return menuOpts, nil
}
