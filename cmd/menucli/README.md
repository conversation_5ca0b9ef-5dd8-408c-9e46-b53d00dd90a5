# OmniChannel Product List CLI

OmniChannel Product List CLI 是一个命令行工具，用于方便地调用 OmniChannel Product List 库的各种功能。

## 安装

确保你已经安装了 Go 1.20 或更高版本，然后运行：

```bash
cd cmd/menucli
go build
```

## 使用方法

### 基本命令格式

```bash
./menucli [command] [flags]
```

### 可用命令

1. **auto-merge**: 自动拉取并合并数据
   ```bash
   ./menucli auto-merge --host=https://api.example.com --token=your-token --store-id=12345
   ```

2. **fetch-only**: 仅拉取数据到暂存目录
   ```bash
   ./menucli fetch-only --host=https://api.example.com --token=your-token --store-id=12345
   ```

3. **merge-temp**: 合并暂存数据到生产目录
   ```bash
   ./menucli merge-temp
   ```

4. **prod-only**: 仅使用生产目录数据
   ```bash
   ./menucli prod-only --store-id=12345
   ```

5. **has-update**: 检查暂存目录是否有更新
   ```bash
   ./menucli has-update
   ```

### 全局标志

- `--host`: API主机地址 (默认: "https://hipos-saas-qa.hexcloud.cn")
- `--token`: 认证令牌
- `--store-id`: 店铺ID
- `--channel`: 渠道代码 (默认: "POS")
- `--lang`: 语言 (默认: "zh-CN")
- `--prod-dir`: 生产数据目录 (默认: "./prod")
- `--temp-dir`: 暂存数据目录 (默认: "./temp")
- `--max-retries`: 最大重试次数 (默认: 3)
- `--retry-delay`: 重试延迟时间 (默认: 500ms)

### 示例

1. 自动拉取并合并数据:
   ```bash
   ./menucli auto-merge \
     --host=https://your-api-host.com \
     --token=your-auth-token \
     --store-id=12345 \
     --channel=POS \
     --prod-dir=/path/to/prod \
     --temp-dir=/path/to/temp
   ```

2. 仅拉取数据到暂存目录:
   ```bash
   ./menucli fetch-only \
     --host=https://your-api-host.com \
     --token=your-auth-token \
     --store-id=12345 \
     --temp-dir=/path/to/temp
   ```

3. 检查暂存目录是否有更新:
   ```bash
   ./menucli has-update \
     --prod-dir=/path/to/prod \
     --temp-dir=/path/to/temp
   ```

### 性能信息

所有命令执行完成后都会显示以下性能信息：
- 操作模式
- 数据大小（如果是获取数据的操作）
- 处理时间

示例输出：
```
操作完成:
  模式: 自动拉取并合并
  数据大小: 2.45 MB
  处理时间: 1.234s
```