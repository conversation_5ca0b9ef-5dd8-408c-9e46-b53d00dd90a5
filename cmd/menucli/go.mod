module hexcloud.cn/hicloud/omnichannel-product-list/cmd/menucli

go 1.20

replace hexcloud.cn/hicloud/omnichannel-product-list => ../..

require (
	github.com/spf13/cobra v1.9.1
	hexcloud.cn/hicloud/omnichannel-product-list v0.0.0-00010101000000-000000000000
)

require github.com/spf13/pflag v1.0.6 // indirect

require (
	github.com/buger/jsonparser v1.1.1 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/huandu/go-clone v1.7.3 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spf13/cast v1.8.0 // indirect
	golang.org/x/sync v0.11.0 // indirect
	golang.org/x/sys v0.0.0-20220715151400-c0bba94af5f8 // indirect
)
