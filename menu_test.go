package omnichannel_product_list

import (
	"context"
	"encoding/json"
	"log"
	"os"
	"testing"
	"time"

	"github.com/buger/jsonparser"
	"hexcloud.cn/hicloud/omnichannel-product-list/model"
)

func TestGetMenuData(t *testing.T) {
	token := "4wdjnZVivVoCVS_B0wUgMfsuPPr8CiYzCOSxeqQiRUw.pYDl9EM8Jl9kEnHmRGC4g36Tjjdcy6N8lLdrwI8xYoE"
	//storeId := uint64(4908316407994810368)
	storeId := "4877537183948374016"
	host := "https://hipos-saas-qa.hexcloud.cn"
	//host := "http://127.0.0.1:8081"
	//host := "https://saas.idimsum.com"

	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list/json"
	//dir := "/Users/<USER>/go_project/src/codeup.aliyun.com/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(DefaultGetFunc(dir))
	err := os.RemoveAll(dir)
	if err != nil {
		return
	}
	logger.Infof("模拟首次抓取数据===============================================")
	start := time.Now()
	menuData, err := GetMenuData(context.Background(), host, token, &GetMenuParams{ChannelCode: "POS", StoreID: storeId, Lang: "zh-CN", PartnerID: "1372"}, withSaveFunc, withGetFunc, WithMaxRetries(0))
	elapsed1 := time.Since(start)
	logger.Infof("首次抓取数据执行时间: %s", elapsed1)
	bs, _ := json.Marshal(menuData)
	logger.Infof("菜单数据获取完成================  数据大小: %s", formatDataSize(len(bs)))
	if err != nil {
		t.Errorf("PullBaseData error: %v", err)
	}

	logger.Infof("模拟增量抓取数据===============================================")
	start = time.Now()
	menuData, err = GetMenuData(context.Background(), host, token, &GetMenuParams{ChannelCode: "POS", StoreID: storeId, Lang: "zh-CN"}, withSaveFunc, withGetFunc, WithMaxRetries(0))
	elapsed2 := time.Since(start)
	logger.Infof("增量获取菜单数据执行时间: %s", elapsed2)

	logger.Infof("时间差异: %s", elapsed1-elapsed2)

	if err := os.WriteFile("menu.json", bs, 0666); err != nil {
		log.Fatal(err)
	}
}

func TestGetMenuDataSingle(t *testing.T) {
	token := "cNcoOdESWIIcvToOhYDjneC1llEsCydSEjyG2jGqMPY.K0qzg_vIZBkzawZpYBqD5fLbeW2LwEj_1bo0-ZiIyac"
	//storeId := uint64(4908316407994810368)
	storeId := "4877537183948374016"
	host := "https://hipos-saas-qa.hexcloud.cn"

	//host := "https://saas.idimsum.com"

	dir := "./json"
	//dir := "/Users/<USER>/go_project/src/codeup.aliyun.com/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(DefaultGetFunc(dir))
	start := time.Now()
	menuData, err := GetMenuData(context.Background(), host, token, &GetMenuParams{ChannelCode: "POS", StoreID: storeId, Lang: "zh-CN", PartnerID: "1372"}, withSaveFunc, withGetFunc, WithMaxRetries(0), WithNotFilterByPrice(true), WithReturnBaseData(true))
	elapsed1 := time.Since(start)
	logger.Infof("抓取数据执行时间: %s", elapsed1)
	bs, _ := json.Marshal(menuData)
	logger.Infof("菜单数据获取完成================  数据大小: %s", formatDataSize(len(bs)))
	if err != nil {
		t.Errorf("PullBaseData error: %v", err)
	}

	if err := os.WriteFile("menu.json", bs, 0666); err != nil {
		log.Fatal(err)
	}
}

func BenchmarkGetMenuData(b *testing.B) {
	token := "z-qVy9SkeQXZjZm2N3_NqT6tzoKvopZbbuSlaOx-3mI.ugo96zHlFF78eIf4nDtlm5GrUV4CwEDba2Ij815nGX8"
	storeId := "4972806615293067264"
	host := "https"
	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(DefaultGetFunc(dir))

	for i := 0; i < b.N; i++ {
		_, err := GetMenuData(context.Background(), host, token, &GetMenuParams{
			ChannelCode: "POS",
			StoreID:     storeId,
			Lang:        "zh-CN",
		}, withSaveFunc, withGetFunc, WithMaxRetries(0))

		if err != nil {
			b.Errorf("GetMenuData error: %v", err)
		}

		//if err := os.WriteFile(fmt.Sprintf("menu_%d.json", i), menuData, 0666); err != nil {
		//	b.Fatalf("write file error: %v", err)
		//}
	}
}

func TestGetMenuDataMarshal(t *testing.T) {
	f := DefaultGetFunc("./json")
	bs, _ := f(DefaultProductListFileName)
	bs, _, _, _ = jsonparser.Get(bs, "rows")
	list := make([]*model.Category, 0)
	err := json.Unmarshal(bs, &list)
	if err != nil {
		t.Errorf("json unmarshal error: %v", err)
	}
}
